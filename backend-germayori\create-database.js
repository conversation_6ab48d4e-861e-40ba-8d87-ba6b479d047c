const { connectDB, closeDB } = require('./config/mongodb');
const { User, Payment } = require('./models');

async function createDatabase() {
    try {
        console.log('🔄 Conectando a MongoDB Atlas...');
        await connectDB();
        console.log('✅ Conectado a MongoDB');

        console.log('🔄 Creando colecciones...');
        
        // Crear colección de usuarios
        await User.createCollection();
        console.log('✅ Colección "users" creada');

        // Crear colección de pagos
        await Payment.createCollection();
        console.log('✅ Colección "payments" creada');

        // Crear usuario de prueba
        const testUser = new User({
            name: 'Usuario Prueba',
            email: '<EMAIL>',
            password: '123456',
            type: 'user',
            plan: 'basic'
        });

        await testUser.save();
        console.log('✅ Usuario de prueba creado');

        // Crear pago de prueba
        const testPayment = new Payment({
            user_id: testUser._id,
            plan: 'basico',
            amount: 45,
            paymentMethod: 'visa',
            status: 'completed',
            paymentDate: new Date(),
            expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 días
            transactionId: 'TEST_001'
        });

        await testPayment.save();
        console.log('✅ Pago de prueba creado');

        console.log('🎉 Base de datos GERMAYORI creada exitosamente!');
        
        await closeDB();
        process.exit(0);

    } catch (error) {
        console.error('❌ Error creando base de datos:', error);
        process.exit(1);
    }
}

createDatabase();
