<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GERMAYORI - Trading Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }
        .btn-primary {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.4);
        }
        .floating {
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center">
    <div class="w-full max-w-md">
        <div class="glass rounded-2xl p-8 glow">
            <div class="text-center mb-8">
                <div class="floating">
                    <h1 class="text-4xl font-black text-white mb-2">
                        <i class="fas fa-rocket mr-3 text-cyan-400"></i>GERMAYORI
                    </h1>
                </div>
                <p class="text-white/80">Trading Platform</p>
            </div>
            <h2 class="text-2xl font-bold text-white mb-6 text-center">Iniciar Sesión</h2>
            <form onsubmit="login(event)">
                <div class="space-y-4">
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Email</label>
                        <input type="email" id="login-email" required class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" placeholder="<EMAIL>" />
                    </div>
                    <div>
                        <label class="block text-white/80 text-sm font-medium mb-2">Contraseña</label>
                        <input type="password" id="login-password" required class="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400" placeholder="••••••••" />
                    </div>
                    <button type="submit" class="w-full btn-primary text-white font-bold py-3 rounded-lg">
                        <i class="fas fa-sign-in-alt mr-2"></i>Entrar
                    </button>
                </div>
            </form>
            <div class="mt-6 text-center">
                <p class="text-white/60">Cuentas de prueba:</p>
                <p class="text-cyan-400 text-sm"><EMAIL> / 123456</p>
                <p class="text-cyan-400 text-sm"><EMAIL> / 123456</p>
            </div>
        </div>
    </div>

    <script>
        const users = {
            '<EMAIL>': { password: '123456', name: 'Admin Germayori' },
            '<EMAIL>': { password: '123456', name: 'Usuario Demo' }
        };

        function login(event) {
            event.preventDefault();
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            if (users[email] && users[email].password === password) {
                alert('Bienvenido ' + users[email].name);
                window.location.href = 'dashboard-simple.html';
            } else {
                alert('Email o contraseña incorrectos');
            }
        }
    </script>
</body>
</html>
