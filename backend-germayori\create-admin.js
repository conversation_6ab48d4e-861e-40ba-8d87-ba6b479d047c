const sqlite3 = require('sqlite3').verbose();

// Conectar a la base de datos
const db = new sqlite3.Database('./germayori.db', (err) => {
    if (err) {
        console.error('Error al conectar con la base de datos:', err);
        process.exit(1);
    } else {
        console.log('✅ Conectado a la base de datos SQLite');
        createAdmin();
    }
});

function createAdmin() {
    // Crear usuario ADMIN
    const adminData = {
        name: 'GERMAYORI Admin',
        email: '<EMAIL>',
        password: 'admin123',
        type: 'admin',
        avatar: 'https://via.placeholder.com/40/FFD700/000000?text=A'
    };

    db.run('INSERT OR REPLACE INTO users (name, email, password, type, avatar) VALUES (?, ?, ?, ?, ?)',
        [adminData.name, adminData.email, adminData.password, adminData.type, adminData.avatar],
        function(err) {
            if (err) {
                console.error('❌ Error creando admin:', err);
            } else {
                console.log('✅ Usuario ADMIN creado exitosamente:');
                console.log('📧 Email: <EMAIL>');
                console.log('🔑 Contraseña: admin123');
                console.log('👑 Tipo: admin');
                console.log('🆔 ID:', this.lastID);
                
                // Crear algunos videos de ejemplo
                createSampleVideos();
            }
        }
    );
}

function createSampleVideos() {
    const sampleVideos = [
        {
            title: 'Introducción a Fair Value Gap',
            description: 'Conceptos básicos de la estrategia GERMAYORI',
            category: 'basico',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
        },
        {
            title: 'Order Blocks Avanzados',
            description: 'Identificación y uso de order blocks institucionales',
            category: 'intermedio',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
        },
        {
            title: 'Estrategia GERMAYORI Completa',
            description: 'La estrategia completa paso a paso',
            category: 'avanzado',
            url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ'
        }
    ];

    let completed = 0;
    sampleVideos.forEach((video, index) => {
        db.run('INSERT INTO videos (title, description, category, url) VALUES (?, ?, ?, ?)',
            [video.title, video.description, video.category, video.url],
            function(err) {
                completed++;
                if (err) {
                    console.error(`❌ Error creando video ${index + 1}:`, err);
                } else {
                    console.log(`✅ Video creado: ${video.title}`);
                }
                
                if (completed === sampleVideos.length) {
                    console.log('\n🎉 ¡CONFIGURACIÓN COMPLETA!');
                    console.log('\n🔑 CREDENCIALES DE ADMIN:');
                    console.log('📧 Email: <EMAIL>');
                    console.log('🔑 Contraseña: admin123');
                    console.log('\n🚀 Ahora puedes:');
                    console.log('1. Ir a http://localhost:4000/registro.html');
                    console.log('2. Registrarte con las credenciales de admin');
                    console.log('3. Acceder a todos los videos sin pagar');
                    console.log('4. Usar el panel admin en http://localhost:4000/panel-simple.html');
                    
                    db.close();
                }
            }
        );
    });
}
