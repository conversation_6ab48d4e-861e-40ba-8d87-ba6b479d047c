// ✅ index.js COMPLETO para backend con GPT-4o y frontend separado
require('dotenv').config();
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const cors = require('cors');
const path = require('path');
const OpenAI = require('openai');

const app = express();
const PORT = 4001;

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

const upload = multer({ dest: 'uploads/' });

// ⚠️ Usamos el chat.html desde la carpeta frontend
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'chat.html'));

});

app.post('/api/analizar-imagen', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) return res.status(400).json({ success: false, message: 'No se recibió imagen.' });

    const imageBuffer = fs.readFileSync(req.file.path);
    const base64Image = imageBuffer.toString('base64');
    const mimeType = req.file.mimetype;

    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `Eres un trader institucional experto en la estrategia RunningPips. Al ver una gráfica, debes identificar si hay entrada en compra o venta, y devolver:
- El par de divisas (o decir "Desconocido" si no se ve claro)
- Precio de entrada (aproximado según el gráfico)
- SL
- TP1, TP2, TP3
- Justificación técnica según BOS, FVG, Liquidez, Order Block.

Responde solo con los datos. No inventes si no ves información clara.`
        },
        {
          role: 'user',
          content: [
            {
              type: 'image_url',
              image_url: {
                url: `data:${mimeType};base64,${base64Image}`
              }
            }
          ]
        }
      ],
      max_tokens: 800,
    });

    const gptResponse = response.choices[0].message.content;

    fs.unlinkSync(req.file.path);

    res.json({ success: true, respuesta: gptResponse });
  } catch (err) {
    console.error('❌ Error con GPT-4o visión:', err.message);
    res.status(500).json({ success: false, message: 'Error analizando imagen con GPT-4o.' });
  }
});

app.listen(PORT, () => {
  console.log(`✅ Servidor GERMAYORI funcionando en http://localhost:${PORT}`);
});
