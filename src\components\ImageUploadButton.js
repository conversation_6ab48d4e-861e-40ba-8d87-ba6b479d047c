import React, { useState } from 'react';
import axios from 'axios';

const ImageUploadButton = ({ onRespuesta }) => {
  const [cargando, setCargando] = useState(false);

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('imagen', file);

    try {
      setCargando(true);
      const res = await axios.post('http://localhost:5000/api/analizar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (res.data?.resultado) {
        onRespuesta(res.data.resultado);
      } else {
        alert('No se recibió respuesta válida del análisis.');
      }
    } catch (error) {
      console.error('Error al subir la imagen:', error);
      alert('Error al analizar la imagen.');
    } finally {
      setCargando(false);
    }
  };

  return (
    <div className="text-center mt-4">
      <label className="inline-block bg-blue-600 text-white px-4 py-2 rounded cursor-pointer hover:bg-blue-700">
        📸 Subir gráfica
        <input
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </label>
      {cargando && <p className="mt-2 text-gray-600">Analizando imagen... ⏳</p>}
    </div>
  );
};

export default ImageUploadButton;
