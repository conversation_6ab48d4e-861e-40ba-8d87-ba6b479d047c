const sqlite3 = require('sqlite3').verbose();

// Conectar a la base de datos
const db = new sqlite3.Database('./germayori.db', (err) => {
    if (err) {
        console.error('❌ Error conectando a la base de datos:', err);
        return;
    }
    console.log('✅ Conectado a la base de datos SQLite');
});

// Agregar <PERSON>
const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: '123456',
    type: 'admin',
    avatar: 'https://via.placeholder.com/40'
};

db.run(`INSERT INTO users (name, email, password, type, avatar) 
        VALUES (?, ?, ?, ?, ?)`,
    [userData.name, userData.email, userData.password, userData.type, userData.avatar],
    function(err) {
        if (err) {
            if (err.message.includes('UNIQUE constraint failed')) {
                console.log('⚠️ <PERSON> ya existe en la base de datos');
            } else {
                console.error('❌ Error agregando usuario:', err);
            }
        } else {
            console.log('🎉 ¡Juan <PERSON> agregado exitosamente!');
            console.log('📊 ID del usuario:', this.lastID);
            console.log('👤 Nombre:', userData.name);
            console.log('📧 Email:', userData.email);
            console.log('🔑 Tipo:', userData.type);
        }
        
        // Mostrar todos los usuarios
        db.all('SELECT * FROM users', (err, users) => {
            if (err) {
                console.error('❌ Error obteniendo usuarios:', err);
            } else {
                console.log('\n📋 USUARIOS EN LA BASE DE DATOS:');
                console.log('================================');
                users.forEach(user => {
                    console.log(`ID: ${user.id} | Nombre: ${user.name} | Email: ${user.email} | Tipo: ${user.type}`);
                });
            }
            
            // Cerrar conexión
            db.close((err) => {
                if (err) {
                    console.error('❌ Error cerrando base de datos:', err);
                } else {
                    console.log('\n✅ Conexión a base de datos cerrada');
                    console.log('🚀 Ahora ve al panel: http://localhost:3001/admin-panel.html');
                }
            });
        });
    }
);
