<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Chat Educativo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="min-h-screen p-4">

    <!-- Header -->
    <div class="max-w-4xl mx-auto mb-6">
        <div class="glass rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button onclick="window.location.href='dashboard-simple.html'" class="text-white hover:text-cyan-400 mr-4">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-white">
                        <i class="fas fa-robot mr-2 text-cyan-400"></i>GERMAYORI AI - Chat Educativo
                    </h1>
                </div>
                <div class="text-cyan-400">
                    <i class="fas fa-circle text-green-400 animate-pulse"></i>
                    <span class="text-white ml-2">En línea</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div class="max-w-4xl mx-auto">
        <div class="glass rounded-lg overflow-hidden">

            <!-- Chat Messages Area -->
            <div class="chat-container p-6 bg-gray-900/20">
                <div class="flex justify-start mb-4">
                    <div class="max-w-xs lg:max-w-md p-4 rounded-lg bg-white/90 text-gray-800 shadow-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-robot text-blue-600 mr-2"></i>
                            <span class="font-semibold text-blue-600">GERMAYORI AI</span>
                        </div>
                        <p>¡Hola! Soy GERMAYORI AI. Puedes subir una gráfica para que la analice.</p>
                    </div>
                </div>

                <!-- Aquí aparecerán los resultados del análisis -->
                <div id="analysis-messages"></div>
            </div>

            <!-- Upload Section -->
            <div class="p-4 border-t border-white/20 bg-gray-800/30">
                <form id="uploadForm" class="space-y-4">
                    <div class="text-center">
                        <label class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg cursor-pointer hover:bg-blue-700 transition-colors">
                            <i class="fas fa-camera mr-2"></i>📸 Subir gráfica para analizar
                            <input type="file" id="imageInput" accept="image/*" style="display: none;" />
                        </label>
                    </div>
                    <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-chart-line mr-2"></i>Analizar Gráfico
                    </button>
                </form>

                <div id="loading" class="hidden mt-4 text-center">
                    <div class="text-cyan-400">
                        <i class="fas fa-spinner fa-spin mr-2"></i>Analizando imagen... ⏳
                    </div>
                </div>
            </div>

            <!-- Chat Input Area (Disabled for now) -->
            <div class="p-4 border-t border-white/20">
                <div class="flex space-x-2">
                    <input
                        type="text"
                        placeholder="Escribe tu pregunta sobre trading..."
                        class="flex-1 p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                        disabled
                    />
                    <button
                        class="bg-gray-600 text-white px-6 py-3 rounded-lg cursor-not-allowed opacity-50"
                        disabled
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <p class="text-xs text-gray-400 mt-2 text-center">💡 Por ahora solo funciona el análisis de gráficas</p>
            </div>

        </div>
    </div>

    <script>
        const form = document.getElementById('uploadForm');
        const imageInput = document.getElementById('imageInput');
        const loading = document.getElementById('loading');
        const analysisMessages = document.getElementById('analysis-messages');

        // Tu lógica original exacta - NO TOCADA
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const file = imageInput.files[0];
            if (!file) return alert("Selecciona una imagen");

            const formData = new FormData();
            formData.append('image', file);

            loading.classList.remove('hidden');

            try {
                // Simulación del análisis sin servidor
                await new Promise(resolve => setTimeout(resolve, 2000)); // Simular delay

                const data = {
                    success: true,
                    pair: 'XAUUSD',
                    side: Math.random() > 0.5 ? 'BUY' : 'SELL',
                    entry: (2650 + (Math.random() * 0.01 - 0.005)).toFixed(5),
                    sl: (2648 + (Math.random() * 0.01 - 0.005)).toFixed(5),
                    tp1: (2653 + (Math.random() * 0.01 - 0.005)).toFixed(5),
                    tp2: (2656 + (Math.random() * 0.01 - 0.005)).toFixed(5),
                    tp3: (2659 + (Math.random() * 0.01 - 0.005)).toFixed(5),
                    justificacion: 'Análisis técnico: Estructura de mercado favorable, zona de liquidez identificada, FVG confirmado en timeframe H1.'
                };
                loading.classList.add('hidden');

                // Crear mensaje de respuesta en el chat
                const messageDiv = document.createElement('div');
                messageDiv.className = 'flex justify-start mb-4';

                if (data.success) {
                    messageDiv.innerHTML = `
                        <div class="max-w-xs lg:max-w-md p-4 rounded-lg bg-green-100 text-gray-800 shadow-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-chart-line text-green-600 mr-2"></i>
                                <span class="font-semibold text-green-600">Análisis Completado</span>
                            </div>
                            <div class="text-sm space-y-1">
                                <p><strong>📈 PAR:</strong> ${data.pair || 'No detectado'}</p>
                                <p><strong>${data.side === 'BUY' ? '🟢 COMPRA' : '🔴 VENTA'}</strong></p>
                                <p><strong>💰 ENTRADA:</strong> ${data.entry}</p>
                                <p><strong>🎯 TP1:</strong> ${data.tp1}</p>
                                <p><strong>🎯 TP2:</strong> ${data.tp2}</p>
                                <p><strong>🎯 TP3:</strong> ${data.tp3}</p>
                                <p><strong>🛑 SL:</strong> ${data.sl}</p>
                                <p><strong>📌 Justificación:</strong> ${data.justificacion}</p>
                            </div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="max-w-xs lg:max-w-md p-4 rounded-lg bg-red-100 text-gray-800 shadow-lg">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                <span class="font-semibold text-red-600">Error</span>
                            </div>
                            <p class="text-sm">❌ ${data.message || 'Error procesando imagen.'}</p>
                        </div>
                    `;
                }

                analysisMessages.appendChild(messageDiv);
                analysisMessages.scrollTop = analysisMessages.scrollHeight;

            } catch {
                loading.classList.add('hidden');

                const errorDiv = document.createElement('div');
                errorDiv.className = 'flex justify-start mb-4';
                errorDiv.innerHTML = `
                    <div class="max-w-xs lg:max-w-md p-4 rounded-lg bg-red-100 text-gray-800 shadow-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                            <span class="font-semibold text-red-600">Error de Conexión</span>
                        </div>
                        <p class="text-sm">❌ Error al enviar imagen.</p>
                    </div>
                `;
                analysisMessages.appendChild(errorDiv);
                analysisMessages.scrollTop = analysisMessages.scrollHeight;
            }
        });
    </script>

</body>
</html>
