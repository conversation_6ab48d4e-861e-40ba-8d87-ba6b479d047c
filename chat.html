<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>GERMAYORI - Chat con Imagen</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white min-h-screen flex items-center justify-center">
  <div class="p-6 bg-gray-800 rounded-xl shadow-xl max-w-xl w-full">
    <h1 class="text-xl font-bold mb-4 text-center">GERMAYORI - Subir Gráfico</h1>
    <form id="uploadForm" class="space-y-4">
      <input type="file" id="imageInput" accept="image/*" class="block w-full p-2 bg-white text-black rounded">
      <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded">Analizar <PERSON></button>
    </form>
    <div id="loading" class="hidden mt-4 text-yellow-400">Analizando imagen...</div>
    <div id="result" class="hidden mt-4 bg-black p-4 rounded text-white whitespace-pre-wrap"></div>
  </div>

  <script>
    const form = document.getElementById('uploadForm');
    const imageInput = document.getElementById('imageInput');
    const loading = document.getElementById('loading');
    const result = document.getElementById('result');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const file = imageInput.files[0];
      if (!file) return alert("Selecciona una imagen");

      const formData = new FormData();
      formData.append('image', file);

      loading.classList.remove('hidden');
      result.classList.add('hidden');

      try {
        const res = await fetch('/api/analizar-imagen', { method: 'POST', body: formData });
        const data = await res.json();
        loading.classList.add('hidden');
        result.classList.remove('hidden');

        if (data.success) {
          result.textContent = `📈 PAR: ${data.pair || 'No detectado'}\n` +
                               `${data.side === 'buy' ? '🟢 COMPRA' : '🔴 VENTA'}\n` +
                               `🎯 TP1: ${data.tp1}\n🎯 TP2: ${data.tp2}\n🎯 TP3: ${data.tp3}\n` +
                               `🛑 SL: ${data.sl}\n\n📌 Justificación: ${data.justificacion}`;
        } else {
          result.textContent = `❌ ${data.message || 'Error procesando imagen.'}`;
        }
      } catch {
        loading.classList.add('hidden');
        result.classList.remove('hidden');
        result.textContent = '❌ Error al enviar imagen.';
      }
    });
  </script>
</body>
</html>
