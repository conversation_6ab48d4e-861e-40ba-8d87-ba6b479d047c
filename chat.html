<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Chat Educativo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .chat-container {
            height: 70vh;
            overflow-y: auto;
        }
        .message-user {
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
        }
        .message-bot {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
    </style>
</head>
<body class="min-h-screen p-4">

    <!-- Header -->
    <div class="max-w-4xl mx-auto mb-6">
        <div class="glass rounded-lg p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button onclick="window.location.href='dashboard-simple.html'" class="text-white hover:text-cyan-400 mr-4">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-white">
                        <i class="fas fa-robot mr-2 text-cyan-400"></i>GERMAYORI AI - Chat Educativo
                    </h1>
                </div>
                <div class="text-cyan-400">
                    <i class="fas fa-circle text-green-400 animate-pulse"></i>
                    <span class="text-white ml-2">En línea</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div class="max-w-4xl mx-auto">
        <div class="glass rounded-lg overflow-hidden">

            <!-- Chat Messages -->
            <div id="chat-messages" class="chat-container p-6 space-y-4">
                <div class="flex justify-start">
                    <div class="message-bot max-w-xs lg:max-w-md p-4 rounded-lg shadow-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-robot text-blue-600 mr-2"></i>
                            <span class="font-semibold text-blue-600">GERMAYORI AI</span>
                        </div>
                        <p>¡Hola! Soy GERMAYORI AI, tu asistente educativo de trading. ¿En qué puedo ayudarte a aprender sobre forex hoy?</p>
                    </div>
                </div>
            </div>

            <!-- Image Upload Section -->
            <div class="p-4 border-t border-white/20">
                <div class="text-center mb-4">
                    <label class="inline-block bg-purple-600 text-white px-4 py-2 rounded cursor-pointer hover:bg-purple-700 transition-colors">
                        <i class="fas fa-camera mr-2"></i>📸 Subir gráfica para analizar
                        <input type="file" accept="image/*" onchange="handleImageUpload(event)" style="display: none;" />
                    </label>
                    <p id="analyzing-text" class="mt-2 text-cyan-400 hidden">Analizando imagen... ⏳</p>
                    <div id="analysis-result" class="mt-4 text-left text-sm text-white space-y-1 hidden"></div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 border-t border-white/20">
                <div class="flex space-x-2">
                    <input
                        type="text"
                        id="chat-input"
                        placeholder="Escribe tu pregunta sobre forex..."
                        class="flex-1 p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                        onkeypress="if(event.key==='Enter') sendMessage()"
                    />
                    <button
                        id="send-button"
                        onclick="sendMessage()"
                        class="bg-cyan-500 hover:bg-cyan-600 text-white px-6 py-3 rounded-lg transition-colors"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Función para agregar mensaje al chat
        function addMessage(message, sender) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');

            if (sender === 'user') {
                messageDiv.className = 'flex justify-end';
                messageDiv.innerHTML = `
                    <div class="message-user max-w-xs lg:max-w-md p-4 rounded-lg shadow-lg text-white">
                        <div class="flex items-center mb-2 justify-end">
                            <span class="font-semibold mr-2">Tú</span>
                            <i class="fas fa-user"></i>
                        </div>
                        <p>${message}</p>
                    </div>
                `;
            } else {
                messageDiv.className = 'flex justify-start';
                messageDiv.innerHTML = `
                    <div class="message-bot max-w-xs lg:max-w-md p-4 rounded-lg shadow-lg">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-robot text-blue-600 mr-2"></i>
                            <span class="font-semibold text-blue-600">GERMAYORI AI</span>
                        </div>
                        <p>${message}</p>
                    </div>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Función para enviar mensaje
        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || isLoading) return;

            addMessage(message, 'user');
            input.value = '';

            isLoading = true;
            document.getElementById('send-button').disabled = true;

            // Simular respuesta del AI
            setTimeout(() => {
                let response = getAIResponse(message);
                addMessage(response, 'bot');
                isLoading = false;
                document.getElementById('send-button').disabled = false;
            }, 1000);
        }

        // Función para obtener respuesta del AI
        function getAIResponse(message) {
            const lowerMessage = message.toLowerCase();

            if (lowerMessage.includes('hola') || lowerMessage.includes('hi')) {
                return '¡Hola! Soy GERMAYORI AI. ¿En qué puedo ayudarte a aprender sobre forex?';
            } else if (lowerMessage.includes('forex') || lowerMessage.includes('divisas')) {
                return 'Forex es el mercado de divisas más grande del mundo. Te recomiendo usar la calculadora de MyFXBook: <a href="https://www.myfxbook.com/forex-calculators/profit-calculator" target="_blank" class="text-blue-600 underline">Calculadora de Profit</a>';
            } else if (lowerMessage.includes('pip')) {
                return 'Los pips son la unidad mínima de cambio en forex. Para EUR/USD, 1 pip = 0.0001. Usa nuestra calculadora para practicar.';
            } else if (lowerMessage.includes('noticia') || lowerMessage.includes('calendario')) {
                return 'Para noticias económicas importantes, revisa el calendario de MyFXBook: <a href="https://www.myfxbook.com/forex-economic-calendar" target="_blank" class="text-blue-600 underline">Calendario Económico</a>';
            } else if (lowerMessage.includes('señal') || lowerMessage.includes('signal')) {
                return 'Las señales de trading son recomendaciones basadas en análisis técnico. Recuerda siempre hacer tu propio análisis antes de operar.';
            } else if (lowerMessage.includes('riesgo') || lowerMessage.includes('risk')) {
                return 'La gestión de riesgo es fundamental. Nunca arriesgues más del 2% de tu capital por operación. Usa siempre stop loss.';
            } else if (lowerMessage.includes('apalancamiento') || lowerMessage.includes('leverage')) {
                return 'El apalancamiento amplifica tanto ganancias como pérdidas. Para principiantes, recomiendo usar apalancamiento bajo (1:10 o 1:20).';
            } else if (lowerMessage.includes('análisis técnico')) {
                return 'El análisis técnico estudia patrones de precios. Conceptos clave: soporte, resistencia, tendencias, y patrones de velas japonesas.';
            } else if (lowerMessage.includes('demo') || lowerMessage.includes('práctica')) {
                return 'Practicar en cuenta demo es esencial. Te permite aprender sin riesgo real. Practica al menos 3-6 meses antes de usar dinero real.';
            } else if (lowerMessage.includes('psicología') || lowerMessage.includes('emociones')) {
                return 'La psicología del trading es crucial. Controla la codicia y el miedo. Mantén disciplina y sigue tu plan de trading.';
            } else if (lowerMessage.includes('estrategia')) {
                return 'Una buena estrategia incluye: punto de entrada, stop loss, take profit, y gestión de riesgo. Prueba tu estrategia en demo primero.';
            } else {
                return 'Interesante pregunta. Te recomiendo revisar nuestros recursos educativos y practicar en la calculadora. ¿Hay algo específico sobre forex que te gustaría aprender?';
            }
        }

        // Función para manejar subida de imágenes
        async function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const analyzingText = document.getElementById('analyzing-text');
            const resultBox = document.getElementById('analysis-result');
            analyzingText.classList.remove('hidden');
            resultBox.classList.add('hidden');
            resultBox.innerHTML = '';

            // Simular análisis de imagen
            setTimeout(() => {
                resultBox.innerHTML = `
                    <div class="bg-white/10 p-4 rounded-lg">
                        <h4 class="font-bold text-cyan-400 mb-2">📊 Análisis de Gráfica</h4>
                        <p><strong>📌 Tendencia:</strong> Alcista en timeframe H4</p>
                        <p><strong>🛡️ Soporte clave:</strong> 1.0850</p>
                        <p><strong>🎯 Resistencia:</strong> 1.0920</p>
                        <p><strong>💡 Recomendación:</strong> Esperar retroceso para entrada en compra</p>
                        <p class="text-yellow-400 text-xs mt-2">⚠️ Esto es una simulación educativa. Siempre haz tu propio análisis.</p>
                    </div>
                `;
                resultBox.classList.remove('hidden');
                analyzingText.classList.add('hidden');
            }, 2000);
        }
    </script>

</body>
</html>
