<!-- SUBIR GRÁFICA PARA ANÁLISIS -->
<div class="p-4 text-center">
  <label class="inline-block bg-blue-600 text-white px-4 py-2 rounded cursor-pointer hover:bg-blue-700">
    📸 Subir gráfica para analizar
    <input type="file" accept="image/*" onchange="handleImageUpload(event)" style="display: none;" />
  </label>
  <p id="analyzing-text" class="mt-2 text-gray-300 hidden">Analizando imagen... ⏳</p>
  <div id="analysis-result" class="mt-4 text-left text-sm text-white space-y-1 hidden"></div>
</div>

<script>
  async function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const analyzingText = document.getElementById('analyzing-text');
    const resultBox = document.getElementById('analysis-result');
    analyzingText.classList.remove('hidden');
    resultBox.classList.add('hidden');
    resultBox.innerHTML = '';

    const formData = new FormData();
    formData.append('imagen', file);

    try {
      const res = await fetch('http://localhost:5000/api/analizar', {
        method: 'POST',
        body: formData
      });

      const data = await res.json();

      if (data.resultado) {
        const r = data.resultado;
        resultBox.innerHTML = `
          <p><strong>📌 Entrada:</strong> ${r.entrada}</p>
          <p><strong>🛡️ Stop Loss:</strong> ${r.stopLoss}</p>
          <p><strong>🎯 Take Profit 1:</strong> ${r.takeProfit1}</p>
          <p><strong>🎯 Take Profit 2:</strong> ${r.takeProfit2}</p>
          <p><strong>🎯 Take Profit 3:</strong> ${r.takeProfit3}</p>
          <p><strong>🧠 Justificación:</strong> ${r.justificacion}</p>
        `;
        resultBox.classList.remove('hidden');
      } else {
        resultBox.innerHTML = '<p class="text-red-300">No se recibió una respuesta válida.</p>';
        resultBox.classList.remove('hidden');
      }

    } catch (error) {
      console.error(error);
      resultBox.innerHTML = '<p class="text-red-300">Error al procesar la imagen.</p>';
      resultBox.classList.remove('hidden');
    }

    analyzingText.classList.add('hidden');
  }
</script>
