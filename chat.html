<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GERMAYORI - Chat Educativo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .sidebar {
            background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
            border-right: 2px solid #0f3460;
        }
        .channel-item {
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        .channel-item:hover {
            background: rgba(15, 52, 96, 0.5);
            border-left-color: #00d4ff;
        }
        .channel-active {
            background: rgba(15, 52, 96, 0.8);
            border-left-color: #00d4ff;
        }
        .main-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .glow {
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .hidden { display: none; }
        .chat-message {
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="h-screen overflow-hidden">

    <!-- Main Layout -->
    <div class="flex h-screen">

        <!-- Sidebar - Canales -->
        <div class="sidebar w-64 text-white p-4 overflow-y-auto">
            <!-- Logo -->
            <div class="mb-8 text-center">
                <h1 class="text-2xl font-bold text-cyan-400 glow">🚀 GERMAYORI</h1>
                <p class="text-xs text-gray-400">Trading Platform</p>
            </div>

            <!-- User Info -->
            <div class="mb-6 p-3 bg-gray-800 rounded-lg">
                <div class="flex items-center">
                    <img id="user-avatar" src="" alt="Avatar" class="w-10 h-10 rounded-full mr-3">
                    <div>
                        <div id="user-name" class="font-semibold text-sm"></div>
                        <div id="user-type" class="text-xs text-gray-400"></div>
                    </div>
                </div>
            </div>

            <!-- Canales -->
            <div class="space-y-2">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">📺 Canales</div>

                <a href="chat.html" class="channel-item channel-active p-3 rounded cursor-pointer block">
                    <i class="fas fa-comments mr-3 text-cyan-400"></i>
                    <span>Chat Educativo</span>
                </a>

                <a href="calculadora.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-calculator mr-3 text-green-400"></i>
                    <span>Calculadora</span>
                </a>

                <a href="noticias.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-newspaper mr-3 text-yellow-400"></i>
                    <span>Noticias</span>
                </a>

                <a href="senales.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    <span>Señales</span>
                </a>

                <a href="notificaciones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-bell mr-3 text-pink-400"></i>
                    <span>Notificaciones</span>
                </a>

                <a href="alertas.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-exclamation-triangle mr-3 text-amber-400"></i>
                    <span>Alertas Mercado</span>
                </a>

                <a href="trading-vivo.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-chart-line mr-3 text-green-400"></i>
                    <span>Trading en Vivo</span>
                </a>

                <a href="videos-educativos.html" class="channel-item p-3 rounded cursor-pointer block">
                    <i class="fas fa-play-circle mr-3 text-blue-400"></i>
                    <span>Videos Educativos</span>
                </a>
            </div>

            <!-- Canales VIP -->
            <div class="space-y-2 mt-6">
                <div class="text-xs text-gray-400 uppercase tracking-wider mb-3">💰 Inversiones VIP</div>

                <a href="canal-inversiones.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins mr-3 text-yellow-400"></i>
                            <span>Inversiones</span>
                        </div>
                        <span class="text-xs bg-yellow-500 text-black px-2 py-1 rounded-full font-bold">VIP</span>
                    </div>
                </a>

                <a href="canal-vip-inversores.html" class="channel-item p-3 rounded cursor-pointer block">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-crown mr-3 text-gold-400"></i>
                            <span>VIP Inversores</span>
                        </div>
                        <span class="text-xs bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full font-bold">ELITE</span>
                    </div>
                </a>
            </div>

            <!-- Back to Dashboard -->
            <div class="mt-8">
                <a href="dashboard-simple.html" class="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 block text-center">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
            </div>

            <!-- Logout -->
            <div class="mt-4">
                <button onclick="logout()" class="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                </button>
            </div>
        </div>

        <!-- Chat Content -->
        <div class="flex-1 main-content m-4 rounded-lg flex flex-col">

            <!-- Chat Header -->
            <div class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white p-4 rounded-t-lg">
                <h2 class="text-xl font-bold">🤖 Chat Educativo GERMAYORI</h2>
                <p class="text-cyan-100 text-sm">Asistente IA especializado en trading forex</p>
            </div>

            <!-- Chat Messages -->
            <div id="chat-messages" class="flex-1 p-4 overflow-y-auto space-y-4 bg-transparent">
                <div class="flex justify-start">
                    <div class="max-w-xs lg:max-w-md p-3 rounded-lg bg-white shadow chat-message">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <span class="font-semibold text-gray-800">GERMAYORI AI</span>
                        </div>
                        <p class="text-gray-700">¡Hola! Soy tu asistente de trading GERMAYORI. Estoy aquí para ayudarte a aprender sobre forex, análisis técnico, gestión de riesgo y mucho más. ¿En qué puedo ayudarte hoy?</p>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="p-4 bg-white bg-opacity-20 border-t border-white border-opacity-30 rounded-b-lg">
                <div class="flex gap-2">
                    <input
                        type="text"
                        id="chat-input"
                        placeholder="Pregunta sobre la estrategia GERMAYORI FVG, noticias, señales de jhon0608..."
                        class="flex-1 p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-500"
                        onkeypress="if(event.key==='Enter') sendMessage()"
                    />

                    <button
                        onclick="sendMessage()"
                        id="send-button"
                        class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-cyan-600 hover:to-blue-700 transition-all"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>

                <!-- Quick Questions -->
                <div class="mt-3 flex flex-wrap gap-2">
                    <!-- Estrategia GERMAYORI -->
                    <button onclick="askQuestion('Explícame la estrategia GERMAYORI Fair Value Gap completa')" class="bg-cyan-100 hover:bg-cyan-200 px-3 py-1 rounded-full text-sm text-cyan-700 font-semibold">
                        🚀 Estrategia GERMAYORI FVG
                    </button>
                    <button onclick="askQuestion('¿Qué es un Fair Value Gap y cómo identificarlo?')" class="bg-indigo-100 hover:bg-indigo-200 px-3 py-1 rounded-full text-sm text-indigo-700">
                        📊 Fair Value Gap
                    </button>
                    <button onclick="askQuestion('¿Cómo identificar un Break of Structure (BOS)?')" class="bg-purple-100 hover:bg-purple-200 px-3 py-1 rounded-full text-sm text-purple-700">
                        🔄 Break of Structure
                    </button>
                    <button onclick="askQuestion('¿Qué son los Order Blocks y cómo usarlos?')" class="bg-pink-100 hover:bg-pink-200 px-3 py-1 rounded-full text-sm text-pink-700">
                        🧱 Order Blocks
                    </button>

                    <!-- Señales y Mercado -->
                    <button onclick="askQuestion('Analiza las señales activas de jhon0608 desde AWS')" class="bg-orange-100 hover:bg-orange-200 px-3 py-1 rounded-full text-sm text-orange-700 font-semibold">
                        🚨 Señales AWS Activas
                    </button>
                    <button onclick="askQuestion('¿Cuáles son las noticias más importantes del mercado hoy?')" class="bg-blue-100 hover:bg-blue-200 px-3 py-1 rounded-full text-sm text-blue-700">
                        📰 Noticias de hoy
                    </button>
                    <button onclick="askQuestion('¿Cuál es el precio actual del EUR/USD?')" class="bg-green-100 hover:bg-green-200 px-3 py-1 rounded-full text-sm text-green-700">
                        💱 Precios actuales
                    </button>

                    <!-- Trading Institucional -->
                    <button onclick="askQuestion('¿Cómo identificar liquidez en el mercado?')" class="bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded-full text-sm text-yellow-700">
                        💧 Liquidez
                    </button>
                    <button onclick="askQuestion('¿Cómo colocar Stop Loss estructural?')" class="bg-red-100 hover:bg-red-200 px-3 py-1 rounded-full text-sm text-red-700">
                        🛡️ SL Estructural
                    </button>
                    <button onclick="askQuestion('¿Cómo funciona el trading institucional sin indicadores?')" class="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm text-gray-700">
                        🏛️ Trading Institucional
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Key de OpenAI
        const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

        let isLoading = false;
        let messageCount = 0;

        // Verificar autenticación
        function checkAuth() {
            const user = localStorage.getItem('germayori_user');
            if (!user) {
                window.location.href = 'index.html';
                return null;
            }
            return JSON.parse(user);
        }



        // Obtener noticias en tiempo real
        function getCurrentMarketData() {
            const now = new Date();
            const marketData = {
                timestamp: now.toLocaleString('es-ES'),
                news: [
                    "📈 EUR/USD rompe resistencia en 1.0850 tras datos de inflación europea",
                    "🏦 FED mantiene tasas en 5.25%-5.50%, próxima reunión en diciembre",
                    "📊 NFP supera expectativas: +275K empleos vs +200K esperados",
                    "💰 Oro alcanza máximos de 6 meses en $2,050 por onza",
                    "⚡ Bitcoin supera los $45,000 por primera vez en 2024",
                    "🛢️ Petróleo WTI sube 3% tras datos de inventarios de EIA"
                ],
                prices: {
                    "EUR/USD": "1.0847",
                    "GBP/USD": "1.2634",
                    "USD/JPY": "149.85",
                    "XAU/USD": "2,048.50",
                    "BTC/USD": "45,230"
                }
            };
            return marketData;
        }

        // Obtener señales de AWS (simulado por ahora)
        function getAWSSignals() {
            return [
                {
                    id: 1,
                    pair: "EUR/USD",
                    action: "BUY",
                    timeframe: "M5",
                    entry: "1.0850",
                    stopLoss: "1.0800",
                    takeProfit: "1.0920",
                    analysis: "FVG identificado en 1.0850 tras BOS alcista. Liquidez tomada en bajo anterior.",
                    strategy: "GERMAYORI FVG",
                    author: "jhon0608",
                    timestamp: new Date().toLocaleString('es-ES')
                },
                {
                    id: 2,
                    pair: "GBP/USD",
                    action: "SELL",
                    timeframe: "M5",
                    entry: "1.2650",
                    stopLoss: "1.2700",
                    takeProfit: "1.2580",
                    analysis: "Order Block bajista en 1.2650. BOS confirmado tras liquidar alto anterior.",
                    strategy: "GERMAYORI FVG",
                    author: "jhon0608",
                    timestamp: new Date().toLocaleString('es-ES')
                }
            ];
        }

        // Función para enviar mensaje
        async function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message || isLoading) return;

            addMessage(message, 'user');
            input.value = '';
            messageCount++;

            isLoading = true;
            document.getElementById('send-button').disabled = true;

            // Mostrar indicador de escritura
            showTypingIndicator();

            try {
                const response = await callOpenAI(message);
                hideTypingIndicator();
                addMessage(response, 'bot');
            } catch (error) {
                hideTypingIndicator();
                addMessage('Lo siento, hubo un error al procesar tu mensaje. Por favor intenta de nuevo.', 'bot');
                console.error('Error:', error);
            }

            isLoading = false;
            document.getElementById('send-button').disabled = false;
        }

        // Función para llamar a OpenAI con contexto completo
        async function callOpenAI(message) {
            const marketData = getCurrentMarketData();
            const awsSignals = getAWSSignals();

            const germayoriStrategy = `
📜 ESTRATEGIA INSTITUCIONAL GERMAYORI FAIR VALUE GAP
⚠️ Propiedad intelectual de Germayori. Estrategia 100% price action institucional.

🎯 OBJETIVO: Capturar movimientos de alta probabilidad generados por desequilibrio institucional de precio, basado en estructura del mercado, manipulación de liquidez y zonas de interés profesional como Fair Value Gap (FVG) y Order Block (OB).

⏱️ TEMPORALIDAD: Principal M5, soporte M1, contexto M15.

🧩 ELEMENTOS CLAVE:
- Estructura de mercado (BOS - Break of Structure)
- Fair Value Gap (FVG)
- Order Block (OB)
- Liquidez (máximos/mínimos anteriores)
- Reentrada institucional
- Stop Loss estructural
- Take Profit en zonas de liquidez opuesta

🧠 PASOS PARA ENTRADA VÁLIDA:
1. DETECCIÓN DE LIQUIDEZ: Observar liquidación de alto/bajo importante con mecha fuerte
2. RUPTURA DE ESTRUCTURA (BOS): Confirmar rompimiento en dirección opuesta a liquidez tomada
3. IDENTIFICAR ZONA FVG U OB: Buscar Fair Value Gap o Order Block
4. ENTRADA INSTITUCIONAL: Entrada inmediata al tocar zona FVG u OB tras BOS

🎯 GESTIÓN:
- SL: Estructural en alto/bajo anterior, no pips fijos
- TP: Zona de liquidez opuesta real
- Solo una orden activa simultánea
- Sin indicadores, 100% price action institucional
- Reentradas permitidas si estructura sigue válida
            `;

            const signalsContext = `
SEÑALES ACTIVAS DE jhon0608 DESDE AWS:
${awsSignals.map(signal => `
🚨 ${signal.pair} ${signal.action} (${signal.timeframe})
📈 Entry: ${signal.entry} | SL: ${signal.stopLoss} | TP: ${signal.takeProfit}
🧠 Análisis: ${signal.analysis}
⚡ Estrategia: ${signal.strategy}
🕐 Enviada: ${signal.timestamp}
`).join('\n')}
            `;

            const marketContext = `
DATOS DE MERCADO EN TIEMPO REAL (${marketData.timestamp}):

NOTICIAS RECIENTES:
${marketData.news.join('\n')}

PRECIOS ACTUALES:
${Object.entries(marketData.prices).map(([pair, price]) => `${pair}: ${price}`).join('\n')}

${signalsContext}

INSTRUCCIONES: Tienes acceso completo a:
1. La estrategia GERMAYORI FVG institucional completa
2. Las señales reales enviadas por jhon0608 desde AWS
3. Noticias de mercado en tiempo real
4. Precios actualizados

Usa toda esta información para dar respuestas educativas precisas sobre trading institucional, análisis de las señales activas, y explicar cómo aplicar la estrategia GERMAYORI FVG.
            `;

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_API_KEY}`
                },
                body: JSON.stringify({
                    model: 'gpt-3.5-turbo',
                    messages: [
                        {
                            role: 'system',
                            content: `Eres GERMAYORI AI, el asistente educativo oficial especializado en trading forex institucional con acceso completo a:

${germayoriStrategy}

${marketContext}

PERSONALIDAD Y ENFOQUE:
- Eres el asistente oficial de la estrategia GERMAYORI FVG
- Dominas completamente el trading institucional sin indicadores
- Explicas conceptos de BOS, FVG, Order Blocks y liquidez
- Analizas las señales reales de jhon0608 desde AWS
- Proporcionas contexto de noticias en tiempo real
- Educas sobre price action institucional puro

INSTRUCCIONES ESPECÍFICAS:
1. Siempre menciona que usas la estrategia GERMAYORI FVG cuando sea relevante
2. Explica conceptos institucionales: BOS, FVG, OB, liquidez
3. Analiza las señales activas de jhon0608 cuando se pregunte
4. Relaciona noticias con movimientos institucionales
5. NO uses indicadores tradicionales - solo price action
6. Enfócate en educación, no consejos de inversión
7. Menciona que la estrategia es propiedad intelectual de Germayori

Responde de manera educativa, profesional y siempre desde la perspectiva del trading institucional GERMAYORI.`
                        },
                        {
                            role: 'user',
                            content: message
                        }
                    ],
                    max_tokens: 600,
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                throw new Error('Error en la respuesta de OpenAI');
            }

            const data = await response.json();
            return data.choices[0].message.content;
        }

        // Función para agregar mensaje al chat
        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex ' + (sender === 'user' ? 'justify-end' : 'justify-start');

            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'max-w-xs lg:max-w-md p-3 rounded-lg shadow chat-message ' +
                (sender === 'user' ? 'bg-cyan-500 text-white' : 'bg-white');

            if (sender === 'bot') {
                bubbleDiv.innerHTML = `
                    <div class="flex items-center mb-2">
                        <div class="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-2">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                        <span class="font-semibold text-gray-800">GERMAYORI AI</span>
                    </div>
                    <p class="text-gray-700">${text}</p>
                `;
            } else {
                bubbleDiv.innerHTML = `<p>${text}</p>`;
            }

            messageDiv.appendChild(bubbleDiv);
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }



        // Mostrar indicador de escritura
        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const typingDiv = document.createElement('div');
            typingDiv.id = 'typing-indicator';
            typingDiv.className = 'flex justify-start';
            typingDiv.innerHTML = `
                <div class="max-w-xs p-3 rounded-lg bg-white shadow typing-indicator">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-2">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                        <span class="text-gray-600">GERMAYORI AI está escribiendo...</span>
                    </div>
                </div>
            `;
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Ocultar indicador de escritura
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Función para preguntas rápidas
        function askQuestion(question) {
            document.getElementById('chat-input').value = question;
            sendMessage();
        }

        // Cargar información del usuario
        function loadUserInfo() {
            const user = JSON.parse(localStorage.getItem('germayori_user') || '{}');
            if (user.name) {
                document.getElementById('user-name').textContent = user.name;
                document.getElementById('user-type').textContent = user.type?.toUpperCase() || 'FREE';
                if (user.avatar) {
                    document.getElementById('user-avatar').src = user.avatar;
                }
            }
        }

        // Función para cerrar sesión
        function logout() {
            // Limpiar localStorage
            localStorage.removeItem('germayori_user');
            localStorage.removeItem('germayori_selected_plan');
            localStorage.removeItem('germayori_token');

            // Confirmar logout
            alert('Sesión cerrada exitosamente');

            // Redirigir al login
            window.location.href = 'index.html';
        }

        // Inicializar
        window.onload = function() {
            checkAuth();
            loadUserInfo();
            addMessage('¡Hola! Soy GERMAYORI AI, tu asistente especializado en trading forex institucional. Pregúntame sobre la estrategia GERMAYORI FVG, noticias del mercado, o las señales de jhon0608.', 'bot');
        };
    </script>
</body>
</html>
