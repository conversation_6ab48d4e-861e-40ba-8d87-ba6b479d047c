const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// MongoDB (solo para usuarios y pagos)
const { connectDB, closeDB } = require('./config/mongodb');
const { User, Payment } = require('./models');

// Servicios AWS
const { createTablesIfNotExist, createS3BucketIfNotExists } = require('./config/aws');
const awsServices = require('./services/awsServices');
const myfxbookService = require('./services/myfxbookService');
const forexNewsService = require('./services/forexNewsService');
const expirationService = require('./services/expirationService');

const app = express();
const PORT = process.env.PORT || 3000;

// Variables globales
let mongoConnected = false;

// Middleware de seguridad
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 100, // máximo 100 requests por ventana
    message: 'Demasiadas solicitudes, intenta de nuevo más tarde'
});
app.use('/api/', limiter);

// Middleware básico
app.use(cors({
    origin: ['http://127.0.0.1:5500', 'http://localhost:5500', 'http://localhost:3000'],
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, '..')));

// Crear/conectar bases de datos
const db = new sqlite3.Database('./germayori.db', (err) => {
    if (err) {
        console.error('Error al conectar con SQLite:', err);
    } else {
        console.log('✅ SQLite conectado (señales, noticias, alertas)');
        initDatabase();
    }
});

// Inicializar MongoDB para usuarios y pagos
async function initMongoDB() {
    try {
        console.log('🔄 Conectando a MongoDB (usuarios y pagos)...');
        await connectDB();
        mongoConnected = true;
        console.log('✅ MongoDB conectado (usuarios y pagos)');
    } catch (error) {
        console.log('⚠️ MongoDB no disponible, usando SQLite para usuarios');
        mongoConnected = false;
    }
}

// Inicializar tablas
function initDatabase() {
    // Tabla de usuarios
    db.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        type TEXT DEFAULT 'user',
        avatar TEXT DEFAULT 'https://via.placeholder.com/40',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de señales
    db.run(`CREATE TABLE IF NOT EXISTS signals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        pair TEXT NOT NULL,
        type TEXT NOT NULL,
        entry_price REAL NOT NULL,
        stop_loss REAL NOT NULL,
        take_profit REAL NOT NULL,
        analysis TEXT,
        risk_level TEXT DEFAULT 'medium',
        timeframe TEXT DEFAULT 'H1',
        status TEXT DEFAULT 'active',
        pips REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    // Tabla de noticias
    db.run(`CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        category TEXT,
        impact TEXT DEFAULT 'medium',
        currency TEXT,
        source TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de alertas
    db.run(`CREATE TABLE IF NOT EXISTS alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        type TEXT NOT NULL,
        symbol TEXT,
        message TEXT NOT NULL,
        level TEXT DEFAULT 'medium',
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    console.log('✅ Tablas de base de datos inicializadas');
}

// RUTAS DE AUTENTICACIÓN
app.post('/api/login', async (req, res) => {
    const { email, password } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const user = await User.findOne({ email, password });
            if (user) {
                // Verificar si la cuenta ha expirado
                const now = new Date();
                if (user.expiryDate && now > user.expiryDate) {
                    return res.status(403).json({
                        error: 'Tu suscripción ha expirado. Por favor, renueva tu plan.',
                        expired: true
                    });
                }

                // Verificar si la cuenta está activa
                if (!user.isActive) {
                    return res.status(403).json({
                        error: 'Tu cuenta está desactivada. Contacta al soporte.',
                        inactive: true
                    });
                }

                user.lastLogin = new Date();
                await user.save();

                res.json({
                    success: true,
                    user: {
                        id: user._id,
                        name: user.name,
                        email: user.email,
                        type: user.type,
                        avatar: user.avatar,
                        plan: user.plan,
                        planPrice: user.planPrice,
                        isActive: user.isActive,
                        expiryDate: user.expiryDate,
                        lastLogin: user.lastLogin
                    }
                });
            } else {
                res.status(401).json({ error: 'Credenciales inválidas' });
            }
        } else {
            // Fallback a SQLite
            db.get('SELECT * FROM users WHERE email = ? AND password = ?', [email, password], (err, user) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else if (user) {
                    res.json({
                        success: true,
                        user: {
                            id: user.id,
                            name: user.name,
                            email: user.email,
                            type: user.type,
                            avatar: user.avatar
                        }
                    });
                } else {
                    res.status(401).json({ error: 'Credenciales inválidas' });
                }
            });
        }
    } catch (error) {
        console.error('Error en login:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/register', async (req, res) => {
    const { name, email, password } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const existingUser = await User.findOne({ email });
            if (existingUser) {
                return res.status(400).json({ error: 'El email ya está registrado' });
            }

            // Calcular fecha de expiración (30 días)
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + 30);

            // Obtener plan y precio del request
            const userPlan = req.body.plan || 'basico';
            const userPlanPrice = req.body.planPrice || 45;

            const newUser = new User({
                name,
                email,
                password,
                type: 'user',
                avatar: 'https://via.placeholder.com/40',
                plan: userPlan,
                planPrice: userPlanPrice,
                isActive: true,
                lastLogin: new Date(),
                expiryDate: expiryDate
            });

            const savedUser = await newUser.save();

            res.json({
                success: true,
                user: {
                    id: savedUser._id,
                    name: savedUser.name,
                    email: savedUser.email,
                    type: savedUser.type,
                    avatar: savedUser.avatar,
                    plan: savedUser.plan,
                    planPrice: savedUser.planPrice,
                    isActive: savedUser.isActive,
                    expiryDate: savedUser.expiryDate,
                    lastLogin: savedUser.lastLogin
                }
            });
        } else {
            // Fallback a SQLite
            db.run('INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
                [name, email, password],
                function(err) {
                    if (err) {
                        if (err.message.includes('UNIQUE constraint failed')) {
                            res.status(400).json({ error: 'El email ya está registrado' });
                        } else {
                            res.status(500).json({ error: 'Error del servidor' });
                        }
                    } else {
                        res.json({
                            success: true,
                            user: {
                                id: this.lastID,
                                name,
                                email,
                                type: 'user',
                                avatar: 'https://via.placeholder.com/40'
                            }
                        });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error en registro:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// RUTAS DE SEÑALES
app.get('/api/signals', (req, res) => {
    db.all(`SELECT s.*, u.name as user_name
            FROM signals s
            LEFT JOIN users u ON s.user_id = u.id
            ORDER BY s.created_at DESC`,
        (err, signals) => {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json(signals);
            }
        }
    );
});

app.post('/api/signals', (req, res) => {
    const { user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe } = req.body;

    db.run(`INSERT INTO signals (user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

app.put('/api/signals/:id', (req, res) => {
    const { id } = req.params;
    const { status, pips } = req.body;

    db.run('UPDATE signals SET status = ?, pips = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [status, pips, id],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true });
            }
        }
    );
});

app.delete('/api/signals/:id', (req, res) => {
    const { id } = req.params;

    db.run('DELETE FROM signals WHERE id = ?', [id], function(err) {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json({ success: true });
        }
    });
});

// RUTAS DE NOTICIAS
app.get('/api/news', (req, res) => {
    db.all('SELECT * FROM news ORDER BY created_at DESC LIMIT 20', (err, news) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(news);
        }
    });
});

app.post('/api/news', (req, res) => {
    const { title, content, category, impact, currency, source } = req.body;

    db.run('INSERT INTO news (title, content, category, impact, currency, source) VALUES (?, ?, ?, ?, ?, ?)',
        [title, content, category, impact, currency, source],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// RUTAS DE ALERTAS
app.get('/api/alerts', (req, res) => {
    db.all('SELECT * FROM alerts ORDER BY created_at DESC LIMIT 50', (err, alerts) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(alerts);
        }
    });
});

app.post('/api/alerts', (req, res) => {
    const { user_id, type, symbol, message, level } = req.body;

    db.run('INSERT INTO alerts (user_id, type, symbol, message, level) VALUES (?, ?, ?, ?, ?)',
        [user_id, type, symbol, message, level],
        function(err) {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, id: this.lastID });
            }
        }
    );
});

// ==================== RUTAS DE PAGOS (MongoDB) ====================

app.get('/api/payments', async (req, res) => {
    try {
        if (mongoConnected) {
            const payments = await Payment.find()
                .populate('user_id', 'name email')
                .sort({ createdAt: -1 });

            res.json(payments);
        } else {
            res.json([]); // Sin MongoDB, no hay pagos
        }
    } catch (error) {
        console.error('Error obteniendo pagos:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/payments', async (req, res) => {
    const { user_id, plan, amount, paymentMethod } = req.body;

    try {
        if (mongoConnected) {
            // Calcular fecha de expiración (30 días)
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + 30);

            const newPayment = new Payment({
                user_id,
                plan,
                amount,
                paymentMethod,
                status: 'completed', // Por ahora, marcar como completado
                paymentDate: new Date(),
                expiryDate,
                transactionId: `GERM_${Date.now()}`
            });

            const savedPayment = await newPayment.save();
            res.json({ success: true, id: savedPayment._id });
        } else {
            res.status(503).json({ error: 'Sistema de pagos no disponible' });
        }
    } catch (error) {
        console.error('Error creando pago:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// Estado de las bases de datos
app.get('/api/db-status', (req, res) => {
    res.json({
        mongodb: mongoConnected,
        sqlite: true,
        users: mongoConnected ? 'MongoDB' : 'SQLite',
        payments: mongoConnected ? 'MongoDB' : 'Disabled',
        signals: 'SQLite',
        news: 'SQLite',
        alerts: 'SQLite'
    });
});

// ==================== RUTAS DE PAGOS (MongoDB) ====================

app.get('/api/payments', async (req, res) => {
    try {
        if (mongoConnected) {
            const payments = await Payment.find()
                .populate('user_id', 'name email')
                .sort({ createdAt: -1 });

            res.json(payments);
        } else {
            res.json([]);
        }
    } catch (error) {
        console.error('Error obteniendo pagos:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/payments', async (req, res) => {
    const { user_id, plan, amount, paymentMethod } = req.body;

    try {
        if (mongoConnected) {
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + 30);

            const newPayment = new Payment({
                user_id,
                plan,
                amount,
                paymentMethod,
                status: 'completed',
                paymentDate: new Date(),
                expiryDate,
                transactionId: `GERM_${Date.now()}`
            });

            const savedPayment = await newPayment.save();
            res.json({ success: true, id: savedPayment._id });
        } else {
            res.status(503).json({ error: 'Sistema de pagos no disponible' });
        }
    } catch (error) {
        console.error('Error creando pago:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// Estado de las bases de datos
app.get('/api/db-status', (req, res) => {
    res.json({
        mongodb: mongoConnected,
        sqlite: true,
        users: mongoConnected ? 'MongoDB' : 'SQLite',
        payments: mongoConnected ? 'MongoDB' : 'Disabled',
        signals: 'SQLite',
        news: 'SQLite',
        alerts: 'SQLite'
    });
});

// ==================== RUTAS AWS ====================

// RUTAS DE MYFXBOOK
app.get('/api/myfxbook/portfolio', async (req, res) => {
    try {
        const result = await myfxbookService.getPortfolioData();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo datos del portfolio' });
    }
});

app.get('/api/myfxbook/trades', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const result = await myfxbookService.getTradeHistory(limit);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo historial de trades' });
    }
});

app.get('/api/myfxbook/stats', async (req, res) => {
    try {
        const result = await myfxbookService.getPerformanceStats();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo estadísticas' });
    }
});

app.get('/api/myfxbook/balance', async (req, res) => {
    try {
        const result = await myfxbookService.getRealTimeBalance();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo balance en tiempo real' });
    }
});

// RUTAS DE NOTICIAS FOREX
app.get('/api/forex/news', async (req, res) => {
    try {
        const result = await forexNewsService.getRealTimeNews();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo noticias' });
    }
});

app.get('/api/forex/calendar', async (req, res) => {
    try {
        const result = await forexNewsService.getEconomicCalendar();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo calendario económico' });
    }
});

app.get('/api/forex/alerts', async (req, res) => {
    try {
        const result = await forexNewsService.getMarketAlerts();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo alertas de mercado' });
    }
});

app.get('/api/forex/sentiment', async (req, res) => {
    try {
        const result = await forexNewsService.getMarketSentiment();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo sentimiento del mercado' });
    }
});

// RUTAS AWS ALTERNATIVAS (para cuando AWS esté configurado)
app.get('/api/aws/signals', async (req, res) => {
    try {
        const signals = await awsServices.getSignals();
        res.json({ success: true, data: signals });
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo señales de AWS' });
    }
});

app.post('/api/aws/signals', async (req, res) => {
    try {
        const result = await awsServices.createSignal(req.body);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error creando señal en AWS' });
    }
});

// RUTAS ADICIONALES PARA ADMIN PANEL
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.get('/api/aws/users', async (req, res) => {
    try {
        const users = await awsServices.getUsers();
        res.json({ success: true, data: users });
    } catch (error) {
        console.log('AWS no disponible, usando SQLite');
        // Fallback a SQLite
        db.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
            if (err) {
                res.status(500).json({ error: 'Error del servidor' });
            } else {
                res.json({ success: true, data: users });
            }
        });
    }
});

app.get('/api/users', (req, res) => {
    db.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
        if (err) {
            res.status(500).json({ error: 'Error del servidor' });
        } else {
            res.json(users);
        }
    });
});

app.get('/api/aws/test', async (req, res) => {
    try {
        // Probar conexión con DynamoDB
        const testResult = await awsServices.testConnection();
        res.json({ success: true, aws: 'connected', result: testResult });
    } catch (error) {
        res.status(500).json({ success: false, aws: 'disconnected', error: error.message });
    }
});

app.post('/api/aws/init', async (req, res) => {
    try {
        console.log('🔄 Inicializando AWS desde admin panel...');
        await createTablesIfNotExist();
        await createS3BucketIfNotExists();
        res.json({ success: true, message: 'AWS inicializado correctamente' });
    } catch (error) {
        console.error('Error inicializando AWS:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Ruta para servir archivos HTML
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index.html'));
});

// Inicializar servicios
async function initializeServices() {
    try {
        console.log('🔄 Inicializando MongoDB...');
        await initMongoDB();

        console.log('🔄 Inicializando servicios AWS...');
        await createTablesIfNotExist();
        await createS3BucketIfNotExists();

        console.log('🔄 Iniciando monitoreo de noticias...');
        forexNewsService.startNewsMonitoring();

        console.log('✅ Sistema híbrido inicializado correctamente');
        console.log('📊 Usuarios y Pagos:', mongoConnected ? 'MongoDB' : 'SQLite');
        console.log('📊 Señales, Noticias, Alertas: SQLite');
    } catch (error) {
        console.error('❌ Error inicializando servicios:', error);
    }
}

// Iniciar servidor
app.listen(PORT, async () => {
    console.log(`🚀 Servidor GERMAYORI corriendo en http://localhost:${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard.html`);
    console.log(`🔗 MyFXBook Portfolio: https://www.myfxbook.com/portfolio/germayori/11537608`);

    // Inicializar servicios AWS
    await initializeServices();
});

// Manejar cierre graceful
process.on('SIGINT', async () => {
    console.log('🔄 Cerrando servidor GERMAYORI...');

    // Detener monitoreo de noticias
    forexNewsService.stopNewsMonitoring();

    // Cerrar MongoDB
    if (mongoConnected) {
        await closeDB();
    }

    // Cerrar SQLite
    db.close((err) => {
        if (err) {
            console.error(err.message);
        } else {
            console.log('✅ Conexiones cerradas');
        }
        console.log('✅ Servidor GERMAYORI cerrado correctamente');
        process.exit(0);
    });
});
