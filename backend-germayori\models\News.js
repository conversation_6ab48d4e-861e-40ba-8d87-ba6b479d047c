const mongoose = require('mongoose');

const newsSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    content: {
        type: String,
        default: ''
    },
    category: {
        type: String,
        enum: ['forex', 'crypto', 'stocks', 'commodities', 'economic', 'general'],
        default: 'forex'
    },
    impact: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
    },
    currency: {
        type: String,
        uppercase: true
    },
    source: {
        type: String,
        default: 'ForexFactory'
    },
    url: {
        type: String,
        default: ''
    },
    imageUrl: {
        type: String,
        default: ''
    },
    isActive: {
        type: Boolean,
        default: true
    },
    publishedAt: {
        type: Date,
        default: Date.now
    },
    // Campos adicionales para noticias de Forex Factory
    eventTime: {
        type: Date
    },
    actual: {
        type: String,
        default: ''
    },
    forecast: {
        type: String,
        default: ''
    },
    previous: {
        type: String,
        default: ''
    }
}, {
    timestamps: true
});

// Índices para optimizar b<PERSON>quedas
newsSchema.index({ category: 1 });
newsSchema.index({ impact: 1 });
newsSchema.index({ currency: 1 });
newsSchema.index({ publishedAt: -1 });
newsSchema.index({ createdAt: -1 });

module.exports = mongoose.model('News', newsSchema);
