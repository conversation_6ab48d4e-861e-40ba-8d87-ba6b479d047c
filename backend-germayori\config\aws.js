const AWS = require('aws-sdk');
require('dotenv').config();

// Configurar AWS SDK
AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'us-east-1'
});

// Servicios AWS
const dynamodb = new AWS.DynamoDB.DocumentClient();
const s3 = new AWS.S3();
const lambda = new AWS.Lambda();

// Configuración de tablas DynamoDB
const TABLES = {
    USERS: process.env.DYNAMODB_USERS_TABLE || 'germayori-users',
    SIGNALS: process.env.DYNAMODB_SIGNALS_TABLE || 'germayori-signals',
    NEWS: process.env.DYNAMODB_NEWS_TABLE || 'germayori-news',
    ALERTS: process.env.DYNAMODB_ALERTS_TABLE || 'germayori-alerts'
};

// Configuración S3
const S3_BUCKET = process.env.S3_BUCKET_NAME || 'germayori-storage';

// Función para crear tablas DynamoDB si no existen
async function createTablesIfNotExist() {
    const dynamodbAdmin = new AWS.DynamoDB();
    
    try {
        // Tabla de usuarios
        await createTableIfNotExists(dynamodbAdmin, TABLES.USERS, {
            AttributeDefinitions: [
                { AttributeName: 'id', AttributeType: 'S' },
                { AttributeName: 'email', AttributeType: 'S' }
            ],
            KeySchema: [
                { AttributeName: 'id', KeyType: 'HASH' }
            ],
            GlobalSecondaryIndexes: [
                {
                    IndexName: 'email-index',
                    KeySchema: [
                        { AttributeName: 'email', KeyType: 'HASH' }
                    ],
                    Projection: { ProjectionType: 'ALL' },
                    BillingMode: 'PAY_PER_REQUEST'
                }
            ],
            BillingMode: 'PAY_PER_REQUEST'
        });

        // Tabla de señales
        await createTableIfNotExists(dynamodbAdmin, TABLES.SIGNALS, {
            AttributeDefinitions: [
                { AttributeName: 'id', AttributeType: 'S' },
                { AttributeName: 'user_id', AttributeType: 'S' },
                { AttributeName: 'created_at', AttributeType: 'S' }
            ],
            KeySchema: [
                { AttributeName: 'id', KeyType: 'HASH' }
            ],
            GlobalSecondaryIndexes: [
                {
                    IndexName: 'user-signals-index',
                    KeySchema: [
                        { AttributeName: 'user_id', KeyType: 'HASH' },
                        { AttributeName: 'created_at', KeyType: 'RANGE' }
                    ],
                    Projection: { ProjectionType: 'ALL' },
                    BillingMode: 'PAY_PER_REQUEST'
                }
            ],
            BillingMode: 'PAY_PER_REQUEST'
        });

        // Tabla de noticias
        await createTableIfNotExists(dynamodbAdmin, TABLES.NEWS, {
            AttributeDefinitions: [
                { AttributeName: 'id', AttributeType: 'S' },
                { AttributeName: 'created_at', AttributeType: 'S' }
            ],
            KeySchema: [
                { AttributeName: 'id', KeyType: 'HASH' }
            ],
            GlobalSecondaryIndexes: [
                {
                    IndexName: 'created-at-index',
                    KeySchema: [
                        { AttributeName: 'created_at', KeyType: 'HASH' }
                    ],
                    Projection: { ProjectionType: 'ALL' },
                    BillingMode: 'PAY_PER_REQUEST'
                }
            ],
            BillingMode: 'PAY_PER_REQUEST'
        });

        // Tabla de alertas
        await createTableIfNotExists(dynamodbAdmin, TABLES.ALERTS, {
            AttributeDefinitions: [
                { AttributeName: 'id', AttributeType: 'S' },
                { AttributeName: 'user_id', AttributeType: 'S' },
                { AttributeName: 'created_at', AttributeType: 'S' }
            ],
            KeySchema: [
                { AttributeName: 'id', KeyType: 'HASH' }
            ],
            GlobalSecondaryIndexes: [
                {
                    IndexName: 'user-alerts-index',
                    KeySchema: [
                        { AttributeName: 'user_id', KeyType: 'HASH' },
                        { AttributeName: 'created_at', KeyType: 'RANGE' }
                    ],
                    Projection: { ProjectionType: 'ALL' },
                    BillingMode: 'PAY_PER_REQUEST'
                }
            ],
            BillingMode: 'PAY_PER_REQUEST'
        });

        console.log('✅ Todas las tablas DynamoDB están listas');
    } catch (error) {
        console.error('❌ Error configurando DynamoDB:', error);
    }
}

async function createTableIfNotExists(dynamodbAdmin, tableName, tableParams) {
    try {
        await dynamodbAdmin.describeTable({ TableName: tableName }).promise();
        console.log(`✅ Tabla ${tableName} ya existe`);
    } catch (error) {
        if (error.code === 'ResourceNotFoundException') {
            console.log(`🔄 Creando tabla ${tableName}...`);
            await dynamodbAdmin.createTable({
                TableName: tableName,
                ...tableParams
            }).promise();
            
            // Esperar a que la tabla esté activa
            await dynamodbAdmin.waitFor('tableExists', { TableName: tableName }).promise();
            console.log(`✅ Tabla ${tableName} creada exitosamente`);
        } else {
            throw error;
        }
    }
}

// Función para crear bucket S3 si no existe
async function createS3BucketIfNotExists() {
    try {
        await s3.headBucket({ Bucket: S3_BUCKET }).promise();
        console.log(`✅ Bucket S3 ${S3_BUCKET} ya existe`);
    } catch (error) {
        if (error.statusCode === 404) {
            console.log(`🔄 Creando bucket S3 ${S3_BUCKET}...`);
            await s3.createBucket({ 
                Bucket: S3_BUCKET,
                CreateBucketConfiguration: {
                    LocationConstraint: process.env.AWS_REGION || 'us-east-1'
                }
            }).promise();
            console.log(`✅ Bucket S3 ${S3_BUCKET} creado exitosamente`);
        } else {
            console.error('❌ Error verificando bucket S3:', error);
        }
    }
}

module.exports = {
    dynamodb,
    s3,
    lambda,
    TABLES,
    S3_BUCKET,
    createTablesIfNotExist,
    createS3BucketIfNotExists
};
