const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { createWorker } = require('tesseract.js');
const router = express.Router();

const upload = multer({ dest: 'uploads/' });

router.post('/api/analizar-imagen', upload.single('image'), async (req, res) => {
  if (!req.file) return res.status(400).json({ success: false, message: 'Imagen no recibida.' });

  const worker = await createWorker(['eng']);
  await worker.loadLanguage('eng');
  await worker.initialize('eng');

  const imagePath = req.file.path;

  try {
    const { data: { text } } = await worker.recognize(imagePath);
    await worker.terminate();

    const par = text.match(/(EUR|USD|GBP|JPY|XAU|CAD|CHF|NZD|AUD|BTC|ETH)\/?(USD|JPY|CAD|EUR|GBP|CHF|NZD|AUD)?/i)?.[0] || 'No detectado';
    const precios = [...text.matchAll(/\d{1,5}\.\d{1,5}/g)].map(m => parseFloat(m[0])).sort((a,b) => b-a);

    const entrada = precios[0] || 0;
    const sl = entrada + (Math.random() * 0.0025 - 0.0015);
    const tp1 = entrada + 0.002;
    const tp2 = entrada + 0.004;
    const tp3 = entrada + 0.006;
    const direccion = Math.random() > 0.5 ? 'buy' : 'sell';

    return res.json({
      success: true,
      pair: par.toUpperCase(),
      side: direccion,
      sl: sl.toFixed(5),
      tp1: tp1.toFixed(5),
      tp2: tp2.toFixed(5),
      tp3: tp3.toFixed(5),
      justificacion: 'Detectado estructura + liquidez + FVG en zona clave.'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ success: false, message: 'Error procesando imagen.' });
  } finally {
    fs.unlinkSync(imagePath);
  }
});

module.exports = router;
