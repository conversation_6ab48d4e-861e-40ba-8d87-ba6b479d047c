const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { Configuration, OpenAIApi } = require('openai');
require('dotenv').config();

const router = express.Router();
const upload = multer({ dest: 'uploads/' });

// CONFIGURAR GPT-4o VISIÓN
const configuration = new Configuration({
  apiKey: process.env.OPENAI_API_KEY,
});
const openai = new OpenAIApi(configuration);

// RUTA POST PARA ANALIZAR LA IMAGEN
router.post('/api/analizar-imagen', upload.single('image'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: 'Imagen no recibida' });
  }

  const imagePath = req.file.path;
  const imageData = fs.readFileSync(imagePath);
  const base64Image = `data:image/png;base64,${imageData.toString('base64')}`;

  try {
    const completion = await openai.createChatCompletion({
      model: "gpt-4-vision-preview",
      messages: [
        {
          role: "system",
          content: "Eres un analista institucional experto en trading técnico con estrategia RunningPips.",
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `
Analiza esta gráfica usando la estrategia RunningPips.
Detecta si hay BOS, FVG, Order Blocks y Liquidez barrida.

Dame una señal real y clara con:
- Par (si se ve)
- Dirección: Compra o Venta
- Entrada: precio actual o ideal
- SL
- TP1, TP2, TP3
- Justificación técnica clara

Responde corto y directo como un trader profesional.
`,
            },
            {
              type: "image_url",
              image_url: {
                url: base64Image,
              },
            },
          ],
        },
      ],
      max_tokens: 1000,
    });

    const gptResponse = completion.data.choices[0].message.content;

    res.json({ success: true, result: gptResponse });

  } catch (error) {
    console.error("❌ Error con GPT-4o visión:", error?.response?.data || error.message);
    res.status(500).json({ success: false, message: 'Error analizando imagen con IA' });
  } finally {
    fs.unlinkSync(imagePath); // eliminar imagen temporal
  }
});

module.exports = router;
