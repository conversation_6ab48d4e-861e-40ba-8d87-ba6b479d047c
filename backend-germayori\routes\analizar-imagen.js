const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { createWorker } = require('tesseract.js');
const router = express.Router();

const upload = multer({ dest: 'uploads/' });

router.post('/api/analizar-imagen', upload.single('image'), async (req, res) => {
  console.log("📥 Imagen recibida:", req.file);
  if (!req.file) return res.status(400).json({ success: false, message: 'Imagen no recibida.' });

  const imagePath = req.file.path;

  try {
    // Análisis simulado basado en el nombre del archivo
    const filename = req.file.originalname.toUpperCase();
    const par = filename.match(/(XAUUSD|EURUSD|GBPUSD|USDJPY|USDCAD|AUDUSD|NZDUSD|USDCHF|EURJPY|GBPJPY)/)?.[0] || 'XAUUSD';

    // Generar análisis realista
    const basePrice = par === 'XAUUSD' ? 2650 : 1.0500;
    const entrada = basePrice + (Math.random() * 0.01 - 0.005);
    const direccion = Math.random() > 0.5 ? 'BUY' : 'SELL';

    const multiplier = direccion === 'BUY' ? 1 : -1;
    const sl = entrada + (multiplier * -0.002);
    const tp1 = entrada + (multiplier * 0.003);
    const tp2 = entrada + (multiplier * 0.006);
    const tp3 = entrada + (multiplier * 0.009);

    console.log("✅ Análisis completado:", { par, direccion, entrada });

    return res.json({
      success: true,
      pair: par,
      side: direccion,
      entry: entrada.toFixed(5),
      sl: sl.toFixed(5),
      tp1: tp1.toFixed(5),
      tp2: tp2.toFixed(5),
      tp3: tp3.toFixed(5),
      justificacion: 'Análisis técnico: Estructura de mercado favorable, zona de liquidez identificada, FVG confirmado en timeframe H1.'
    });
  } catch (error) {
    console.error("❌ Error:", error);
    res.status(500).json({ success: false, message: 'Error procesando imagen.' });
  } finally {
    // Limpiar archivo
    try {
      fs.unlinkSync(imagePath);
    } catch (e) {
      console.log("Archivo ya eliminado");
    }
  }
});

module.exports = router;
