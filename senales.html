<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Señales GERMAYORI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white min-h-screen flex flex-col items-center justify-center p-4">
    
    <div class="text-center max-w-md">
        <h1 class="text-4xl font-bold mb-6">📈 Señales GERMAYORI</h1>
        <p class="text-gray-300 mb-8">Análisis automático de gráficas con IA</p>

        <!-- Formulario de análisis integrado -->
        <form id="formulario" class="bg-gray-800 p-6 rounded-xl w-full border border-gray-700 mb-6">
            <label for="imagen" class="block mb-4 text-lg">📄 Sube una gráfica:</label>
            <input type="file" id="imagen" name="image" accept="image/*" required class="mb-4 w-full bg-gray-700 p-3 rounded-lg" />
            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-xl w-full">
                <i class="fas fa-chart-line mr-2"></i>📊 Analizar
            </button>
        </form>

        <!-- Resultado del análisis -->
        <div id="resultado" class="w-full p-6 bg-gray-800 rounded-xl border border-gray-700 hidden mb-6">
            <h2 class="text-xl font-semibold mb-4">📋 Resultado:</h2>
            <pre id="respuesta" class="whitespace-pre-wrap text-sm text-green-400 text-left"></pre>
        </div>

        <!-- Botón para regresar al dashboard -->
        <button onclick="regresarDashboard()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg w-full">
            <i class="fas fa-home mr-2"></i>
            Regresar al Dashboard
        </button>
    </div>

    <script>
        // Código de análisis (copiado del que ya funciona)
        const form = document.getElementById('formulario');
        const resultado = document.getElementById('resultado');
        const respuesta = document.getElementById('respuesta');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            console.log("Formulario enviado");

            const archivo = document.getElementById('imagen').files[0];
            if (!archivo) return alert("Selecciona una imagen");

            console.log("Archivo seleccionado:", archivo.name);

            const formData = new FormData();
            formData.append('image', archivo);

            try {
                console.log("Enviando a servidor...");
                const res = await fetch('/api/analizar-imagen', {
                    method: 'POST',
                    body: formData
                });

                console.log("Respuesta recibida:", res.status);
                const data = await res.json();
                console.log("Datos:", data);

                respuesta.textContent = data.success ? data.respuesta : "❌ " + data.message;
                resultado.classList.remove('hidden');
            } catch (err) {
                console.error("Error:", err);
                respuesta.textContent = "❌ Error de conexión con el servidor";
                resultado.classList.remove('hidden');
            }
        });

        function regresarDashboard() {
            // Regresar al dashboard
            window.location.href = 'dashboard-simple.html';
        }
    </script>
</body>
</html>
