const mongoose = require('mongoose');

const signalSchema = new mongoose.Schema({
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    pair: {
        type: String,
        required: true,
        uppercase: true
    },
    type: {
        type: String,
        enum: ['BUY', 'SELL', 'buy', 'sell'],
        required: true
    },
    entry_price: {
        type: Number,
        required: true
    },
    stop_loss: {
        type: Number,
        required: true
    },
    take_profit: {
        type: Number,
        required: true
    },
    analysis: {
        type: String,
        default: ''
    },
    risk_level: {
        type: String,
        enum: ['low', 'medium', 'high'],
        default: 'medium'
    },
    timeframe: {
        type: String,
        enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1'],
        default: 'H1'
    },
    status: {
        type: String,
        enum: ['active', 'closed', 'cancelled', 'pending'],
        default: 'active'
    },
    pips: {
        type: Number,
        default: 0
    },
    profit: {
        type: Number,
        default: 0
    },
    // Campos adicionales para señales multi-timeframe
    timeframes: [{
        tf: String,
        entry: Number,
        sl: Number,
        tp: Number
    }],
    // Metadatos
    source: {
        type: String,
        default: 'manual'
    },
    confidence: {
        type: Number,
        min: 0,
        max: 100,
        default: 75
    }
}, {
    timestamps: true
});

// Índices para optimizar búsquedas
signalSchema.index({ pair: 1 });
signalSchema.index({ status: 1 });
signalSchema.index({ user_id: 1 });
signalSchema.index({ createdAt: -1 });
signalSchema.index({ timeframe: 1 });

module.exports = mongoose.model('Signal', signalSchema);
