<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Cha<PERSON> Germayori AI</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white flex flex-col items-center justify-center min-h-screen p-4">
  <h1 class="text-3xl font-bold mb-4">📈 Germayori AI - Análisis de Gráficas</h1>

  <form id="formulario-imagen" class="bg-gray-800 p-4 rounded-xl w-full max-w-md text-center border border-gray-600">
    <label for="imagen" class="block mb-2">📸 Subir gráfica para analizar:</label>
    <input type="file" name="image" id="imagen" accept="image/*" class="mb-4 w-full" required />
    <button type="submit" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-xl">📤 Enviar</button>
  </form>

  <div id="respuesta" class="mt-6 w-full max-w-md p-4 bg-gray-800 rounded-xl border border-gray-600 hidden">
    <h2 class="text-xl font-semibold mb-2">📊 Resultado:</h2>
    <pre id="resultado" class="whitespace-pre-wrap"></pre>
  </div>

  <script>
    const formulario = document.getElementById('formulario-imagen');
    const respuestaDiv = document.getElementById('respuesta');
    const resultadoPre = document.getElementById('resultado');

    formulario.addEventListener('submit', async (e) => {
      e.preventDefault();

      const archivo = document.getElementById('imagen').files[0];
      if (!archivo) return alert("Selecciona una imagen");

      const formData = new FormData();
      formData.append('image', archivo);

      try {
        const res = await fetch('http://localhost:4002/api/analizar-imagen', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();

        if (data.success) {
          resultadoPre.textContent = data.result;
          respuestaDiv.classList.remove('hidden');
        } else {
          resultadoPre.textContent = '❌ Error en el análisis: ' + data.message;
          respuestaDiv.classList.remove('hidden');
        }
      } catch (err) {
        console.error(err);
        resultadoPre.textContent = '❌ Error en la conexión con el servidor';
        respuestaDiv.classList.remove('hidden');
      }
    });
  </script>
</body>
</html>
