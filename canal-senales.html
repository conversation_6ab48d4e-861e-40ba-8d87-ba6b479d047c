<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON></title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white min-h-screen flex flex-col items-center justify-center p-4">
  <h1 class="text-3xl font-bold mb-4">📈 <PERSON><PERSON><PERSON> Ger<PERSON> - Aná<PERSON> Automático</h1>

  <form id="formulario" class="bg-gray-800 p-4 rounded-xl w-full max-w-md text-center border border-gray-700">
    <label for="imagen" class="block mb-2">📄 Sube una gráfica:</label>
    <input type="file" id="imagen" name="image" accept="image/*" required class="mb-4 w-full bg-gray-700 p-2 rounded" />
    <button type="submit" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-xl">📊 Analizar</button>
  </form>

  <div id="resultado" class="mt-6 w-full max-w-md p-4 bg-gray-800 rounded-xl border border-gray-700 hidden">
    <h2 class="text-xl font-semibold mb-2">📋 Resultado:</h2>
    <pre id="respuesta" class="whitespace-pre-wrap text-sm text-green-400"></pre>
  </div>

  <script>
    const form = document.getElementById('formulario');
    const resultado = document.getElementById('resultado');
    const respuesta = document.getElementById('respuesta');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const archivo = document.getElementById('imagen').files[0];
      if (!archivo) return alert("Selecciona una imagen");

      const formData = new FormData();
      formData.append('image', archivo);

      try {
        const res = await fetch('http://localhost:4001/api/analizar-imagen', {
          method: 'POST',
          body: formData
        });

        const data = await res.json();
        respuesta.textContent = data.success ? data.respuesta : "❌ " + data.message;
        resultado.classList.remove('hidden');
      } catch (err) {
        respuesta.textContent = "❌ Error de conexión con el servidor";
        resultado.classList.remove('hidden');
      }
    });
  </script>
</body>
</html>
