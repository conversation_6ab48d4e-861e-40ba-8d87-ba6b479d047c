const sqlite3 = require('sqlite3').verbose();

// Conectar a la base de datos
const db = new sqlite3.Database('./germayori.db', (err) => {
    if (err) {
        console.error('❌ Error conectando a la base de datos:', err);
        return;
    }
    console.log('✅ Conectado a la base de datos SQLite');
    console.log('📊 CONTENIDO DE LA BASE DE DATOS GERMAYORI\n');
    
    viewAllTables();
});

function viewAllTables() {
    // Ver usuarios
    console.log('👥 === USUARIOS ===');
    db.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
        if (err) {
            console.error('Error:', err);
        } else {
            if (users.length === 0) {
                console.log('   📭 No hay usuarios registrados');
            } else {
                users.forEach(user => {
                    console.log(`   🔹 ID: ${user.id} | Nombre: ${user.name} | Email: ${user.email} | Tipo: ${user.type}`);
                });
            }
        }
        
        // Ver señales
        console.log('\n📈 === SEÑALES ===');
        db.all('SELECT * FROM signals ORDER BY created_at DESC LIMIT 10', (err, signals) => {
            if (err) {
                console.error('Error:', err);
            } else {
                if (signals.length === 0) {
                    console.log('   📭 No hay señales');
                } else {
                    signals.forEach(signal => {
                        console.log(`   🔹 ${signal.pair} | ${signal.type} | Entrada: ${signal.entry_price} | Estado: ${signal.status}`);
                    });
                }
            }
            
            // Ver noticias
            console.log('\n📰 === NOTICIAS ===');
            db.all('SELECT * FROM news ORDER BY created_at DESC LIMIT 5', (err, news) => {
                if (err) {
                    console.error('Error:', err);
                } else {
                    if (news.length === 0) {
                        console.log('   📭 No hay noticias');
                    } else {
                        news.forEach(item => {
                            console.log(`   🔹 ${item.title} | Impacto: ${item.impact}`);
                        });
                    }
                }
                
                // Ver alertas
                console.log('\n🚨 === ALERTAS ===');
                db.all('SELECT * FROM alerts ORDER BY created_at DESC LIMIT 5', (err, alerts) => {
                    if (err) {
                        console.error('Error:', err);
                    } else {
                        if (alerts.length === 0) {
                            console.log('   📭 No hay alertas');
                        } else {
                            alerts.forEach(alert => {
                                console.log(`   🔹 ${alert.type} | ${alert.message} | Nivel: ${alert.level}`);
                            });
                        }
                    }
                    
                    // Ver videos
                    console.log('\n🎥 === VIDEOS ===');
                    db.all('SELECT * FROM videos ORDER BY created_at DESC LIMIT 5', (err, videos) => {
                        if (err) {
                            console.error('Error:', err);
                        } else {
                            if (videos.length === 0) {
                                console.log('   📭 No hay videos');
                            } else {
                                videos.forEach(video => {
                                    console.log(`   🔹 ${video.title} | Categoría: ${video.category}`);
                                });
                            }
                        }
                        
                        // Cerrar conexión
                        db.close((err) => {
                            if (err) {
                                console.error(err.message);
                            } else {
                                console.log('\n✅ Conexión cerrada');
                            }
                        });
                    });
                });
            });
        });
    });
}
