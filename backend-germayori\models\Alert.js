const mongoose = require('mongoose');

const alertSchema = new mongoose.Schema({
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    type: {
        type: String,
        enum: ['price', 'news', 'signal', 'system', 'market'],
        required: true
    },
    symbol: {
        type: String,
        uppercase: true
    },
    message: {
        type: String,
        required: true
    },
    level: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'medium'
    },
    status: {
        type: String,
        enum: ['active', 'read', 'dismissed', 'expired'],
        default: 'active'
    },
    // Campos adicionales para alertas de precio
    targetPrice: {
        type: Number
    },
    currentPrice: {
        type: Number
    },
    direction: {
        type: String,
        enum: ['above', 'below', 'equal']
    },
    // Metadatos
    source: {
        type: String,
        default: 'system'
    },
    isRead: {
        type: Boolean,
        default: false
    },
    readAt: {
        type: Date
    },
    expiresAt: {
        type: Date
    }
}, {
    timestamps: true
});

// Índices para optimizar búsquedas
alertSchema.index({ user_id: 1 });
alertSchema.index({ type: 1 });
alertSchema.index({ status: 1 });
alertSchema.index({ level: 1 });
alertSchema.index({ createdAt: -1 });
alertSchema.index({ expiresAt: 1 });

module.exports = mongoose.model('Alert', alertSchema);
