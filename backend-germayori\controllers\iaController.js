// controllers/iaController.js
const { generarAnalisisIA } = require('../utils/openaiClient');

exports.procesarMensajeIA = async (req, res) => {
  try {
    const { mensaje } = req.body;

    if (!mensaje) {
      return res.status(400).json({ error: 'El mensaje es requerido' });
    }

    const resultado = await generarAnalisisIA(mensaje);
    res.json({ resultado });
  } catch (error) {
    console.error('Error IA:', error.message);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
};
// controllers/iaController.js
const { generarAnalisisIA } = require('../utils/openaiClient');

exports.procesarMensajeIA = async (req, res) => {
  try {
    const { mensaje } = req.body;

    if (!mensaje) {
      return res.status(400).json({ error: 'El mensaje es requerido' });
    }

    const resultado = await generarAnalisisIA(mensaje);
    res.json({ resultado });
  } catch (error) {
    console.error('Error IA:', error.message);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
};
