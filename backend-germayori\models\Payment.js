const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    plan: {
        type: String,
        enum: ['senales', 'basico', 'intermedio', 'avanzado', 'completo'],
        required: true
    },
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'USD'
    },
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
        default: 'pending'
    },
    paymentMethod: {
        type: String,
        enum: ['visa', 'mastercard', 'paypal', 'stripe'],
        required: true
    },
    transactionId: {
        type: String,
        unique: true,
        sparse: true
    },
    paymentDate: {
        type: Date
    },
    expiryDate: {
        type: Date,
        required: true
    },
    // Metadatos del pago
    metadata: {
        gateway: String,
        gatewayTransactionId: String,
        gatewayResponse: Object,
        ipAddress: String,
        userAgent: String
    },
    // Facturación
    billing: {
        name: String,
        email: String,
        address: String,
        city: String,
        country: String,
        zipCode: String
    },
    // Notas administrativas
    notes: {
        type: String,
        default: ''
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Índices para optimizar búsquedas
paymentSchema.index({ user_id: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ plan: 1 });
paymentSchema.index({ paymentDate: -1 });
paymentSchema.index({ expiryDate: 1 });
paymentSchema.index({ transactionId: 1 });

// Método para verificar si el pago está activo
paymentSchema.methods.isPaymentActive = function() {
    return this.status === 'completed' &&
           this.expiryDate > new Date() &&
           this.isActive;
};

// Método para obtener días restantes
paymentSchema.methods.getDaysRemaining = function() {
    if (this.status !== 'completed' || !this.isActive) return 0;

    const now = new Date();
    const expiry = new Date(this.expiryDate);
    const diffTime = expiry - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
};

module.exports = mongoose.model('Payment', paymentSchema);
