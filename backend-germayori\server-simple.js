const express = require('express');
const path = require('path');

const app = express();
const PORT = 4001;

// Middleware básico
app.use(express.json());
app.use(express.static(path.join(__dirname, '..')));

// Importar ruta de análisis de imagen
const analizarImagenRuta = require('./routes/analizar-imagen');
app.use(analizarImagenRuta);

// Ruta de salud
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Ruta principal
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index.html'));
});

// Iniciar servidor
app.listen(PORT, () => {
    console.log(`🚀 Servidor GERMAYORI funcionando en http://localhost:${PORT}`);
    console.log(`📊 Panel: http://localhost:${PORT}/panel-simple.html`);
    console.log(`📹 Videos: http://localhost:${PORT}/videos-educativos.html`);
    console.log(`🎮 Dashboard: http://localhost:${PORT}/dashboard-simple.html`);
});

console.log('✅ Servidor iniciado correctamente');
