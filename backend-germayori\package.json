{"name": "germa<PERSON><PERSON>-backend", "version": "1.0.0", "description": "Backend para GERMAYORI Trading Educativo", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"aws-sdk": "^2.1692.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["trading", "education", "api", "express"], "author": "GERMAYORI", "license": "MIT"}