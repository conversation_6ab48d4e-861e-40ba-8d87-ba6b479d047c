import React, { useState } from 'react';
import axios from 'axios';

const App = () => {
  const [respuesta, setRespuesta] = useState(null);
  const [cargando, setCargando] = useState(false);

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('imagen', file);

    try {
      setCargando(true);
      const res = await axios.post('http://localhost:5000/api/analizar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (res.data?.resultado) {
        setRespuesta(res.data.resultado);
      } else {
        alert('No se recibió respuesta válida del análisis.');
      }
    } catch (error) {
      console.error('Error al subir la imagen:', error);
      alert('Error al analizar la imagen.');
    } finally {
      setCargando(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-6">
      <h1 className="text-2xl font-bold mb-4 text-blue-700">📈 Germayori IA - Análisis de Gráficas</h1>

      <label className="bg-blue-600 text-white px-4 py-2 rounded cursor-pointer hover:bg-blue-700">
        📸 Subir Gráfica
        <input
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          style={{ display: 'none' }}
        />
      </label>

      {cargando && <p className="mt-4 text-gray-700">Analizando imagen... ⏳</p>}

      {respuesta && (
        <div className="mt-6 bg-white p-4 rounded shadow w-full max-w-md">
          <p><strong>📌 Entrada:</strong> {respuesta.entrada}</p>
          <p><strong>🛑 SL:</strong> {respuesta.stopLoss}</p>
          <p><strong>🎯 TP1:</strong> {respuesta.takeProfit1}</p>
          <p><strong>🎯 TP2:</strong> {respuesta.takeProfit2}</p>
          <p><strong>🎯 TP3:</strong> {respuesta.takeProfit3}</p>
          <p className="mt-2 text-sm text-gray-600"><strong>Justificación:</strong> {respuesta.justificacion}</p>
        </div>
      )}
    </div>
  );
};

export default App;
