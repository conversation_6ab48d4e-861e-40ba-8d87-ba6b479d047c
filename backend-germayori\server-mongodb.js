const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// MongoDB
const { connectDB, closeDB, isConnected } = require('./config/mongodb');
const { User, Signal, News, Alert } = require('./models');

// SQLite fallback
const sqlite3 = require('sqlite3').verbose();

// Servicios AWS
const { createTablesIfNotExist, createS3BucketIfNotExists } = require('./config/aws');
const awsServices = require('./services/awsServices');
const myfxbookService = require('./services/myfxbookService');
const forexNewsService = require('./services/forexNewsService');

const app = express();
const PORT = process.env.PORT || 3000;

// Variables globales
let mongoConnected = false;
let sqliteDb = null;

// Middleware de seguridad
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 100, // máximo 100 requests por ventana
    message: 'Demasiadas solicitudes, intenta de nuevo más tarde'
});
app.use('/api/', limiter);

// Middleware básico
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, '..')));

// Inicializar bases de datos
async function initializeDatabases() {
    try {
        // Intentar conectar a MongoDB
        console.log('🔄 Intentando conectar a MongoDB...');
        const mongoConnection = await connectDB();

        if (mongoConnection) {
            mongoConnected = true;
            console.log('✅ MongoDB conectado exitosamente');
        } else {
            throw new Error('No se pudo conectar a MongoDB');
        }
    } catch (error) {
        console.log('⚠️ MongoDB no disponible, usando SQLite como fallback');
        mongoConnected = false;

        // Inicializar SQLite como fallback
        sqliteDb = new sqlite3.Database('./germayori.db', (err) => {
            if (err) {
                console.error('Error al conectar con SQLite:', err);
            } else {
                console.log('✅ Conectado a SQLite (fallback)');
                initSQLiteTables();
            }
        });
    }
}

// Inicializar tablas SQLite (fallback)
function initSQLiteTables() {
    if (!sqliteDb) return;

    // Tabla de usuarios
    sqliteDb.run(`CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        type TEXT DEFAULT 'user',
        avatar TEXT DEFAULT 'https://via.placeholder.com/40',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de señales
    sqliteDb.run(`CREATE TABLE IF NOT EXISTS signals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        pair TEXT NOT NULL,
        type TEXT NOT NULL,
        entry_price REAL NOT NULL,
        stop_loss REAL NOT NULL,
        take_profit REAL NOT NULL,
        analysis TEXT,
        risk_level TEXT DEFAULT 'medium',
        timeframe TEXT DEFAULT 'H1',
        status TEXT DEFAULT 'active',
        pips REAL DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    // Tabla de noticias
    sqliteDb.run(`CREATE TABLE IF NOT EXISTS news (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        content TEXT,
        category TEXT,
        impact TEXT DEFAULT 'medium',
        currency TEXT,
        source TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Tabla de alertas
    sqliteDb.run(`CREATE TABLE IF NOT EXISTS alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        type TEXT NOT NULL,
        symbol TEXT,
        message TEXT NOT NULL,
        level TEXT DEFAULT 'medium',
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )`);

    console.log('✅ Tablas SQLite inicializadas');
}

// ==================== RUTAS DE AUTENTICACIÓN ====================

app.post('/api/login', async (req, res) => {
    const { email, password } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const user = await User.findOne({ email, password });
            if (user) {
                // Actualizar último login
                user.lastLogin = new Date();
                await user.save();

                res.json({
                    success: true,
                    user: {
                        id: user._id,
                        name: user.name,
                        email: user.email,
                        type: user.type,
                        avatar: user.avatar,
                        plan: user.plan
                    }
                });
            } else {
                res.status(401).json({ error: 'Credenciales inválidas' });
            }
        } else {
            // Usar SQLite fallback
            sqliteDb.get('SELECT * FROM users WHERE email = ? AND password = ?', [email, password], (err, user) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else if (user) {
                    res.json({
                        success: true,
                        user: {
                            id: user.id,
                            name: user.name,
                            email: user.email,
                            type: user.type,
                            avatar: user.avatar
                        }
                    });
                } else {
                    res.status(401).json({ error: 'Credenciales inválidas' });
                }
            });
        }
    } catch (error) {
        console.error('Error en login:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/register', async (req, res) => {
    const { name, email, password } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const existingUser = await User.findOne({ email });
            if (existingUser) {
                return res.status(400).json({ error: 'El email ya está registrado' });
            }

            const newUser = new User({
                name,
                email,
                password,
                type: 'user',
                avatar: 'https://via.placeholder.com/40'
            });

            const savedUser = await newUser.save();

            res.json({
                success: true,
                user: {
                    id: savedUser._id,
                    name: savedUser.name,
                    email: savedUser.email,
                    type: savedUser.type,
                    avatar: savedUser.avatar,
                    plan: savedUser.plan
                }
            });
        } else {
            // Usar SQLite fallback
            sqliteDb.run('INSERT INTO users (name, email, password) VALUES (?, ?, ?)',
                [name, email, password],
                function(err) {
                    if (err) {
                        if (err.message.includes('UNIQUE constraint failed')) {
                            res.status(400).json({ error: 'El email ya está registrado' });
                        } else {
                            res.status(500).json({ error: 'Error del servidor' });
                        }
                    } else {
                        res.json({
                            success: true,
                            user: {
                                id: this.lastID,
                                name,
                                email,
                                type: 'user',
                                avatar: 'https://via.placeholder.com/40'
                            }
                        });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error en registro:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// ==================== RUTAS DE SEÑALES ====================

app.get('/api/signals', async (req, res) => {
    try {
        if (mongoConnected) {
            // Usar MongoDB con populate para obtener datos del usuario
            const signals = await Signal.find()
                .populate('user_id', 'name email')
                .sort({ createdAt: -1 })
                .limit(50);

            res.json(signals);
        } else {
            // Usar SQLite fallback
            sqliteDb.all(`SELECT s.*, u.name as user_name
                        FROM signals s
                        LEFT JOIN users u ON s.user_id = u.id
                        ORDER BY s.created_at DESC`,
                (err, signals) => {
                    if (err) {
                        res.status(500).json({ error: 'Error del servidor' });
                    } else {
                        res.json(signals);
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error obteniendo señales:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/signals', async (req, res) => {
    const { user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const newSignal = new Signal({
                user_id,
                pair: pair.toUpperCase(),
                type: type.toUpperCase(),
                entry_price,
                stop_loss,
                take_profit,
                analysis,
                risk_level,
                timeframe
            });

            const savedSignal = await newSignal.save();
            res.json({ success: true, id: savedSignal._id });
        } else {
            // Usar SQLite fallback
            sqliteDb.run(`INSERT INTO signals (user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [user_id, pair, type, entry_price, stop_loss, take_profit, analysis, risk_level, timeframe],
                function(err) {
                    if (err) {
                        res.status(500).json({ error: 'Error del servidor' });
                    } else {
                        res.json({ success: true, id: this.lastID });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error creando señal:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.put('/api/signals/:id', async (req, res) => {
    const { id } = req.params;
    const { status, pips } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const updatedSignal = await Signal.findByIdAndUpdate(
                id,
                { status, pips, updatedAt: new Date() },
                { new: true }
            );

            if (updatedSignal) {
                res.json({ success: true });
            } else {
                res.status(404).json({ error: 'Señal no encontrada' });
            }
        } else {
            // Usar SQLite fallback
            sqliteDb.run('UPDATE signals SET status = ?, pips = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [status, pips, id],
                function(err) {
                    if (err) {
                        res.status(500).json({ error: 'Error del servidor' });
                    } else {
                        res.json({ success: true });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error actualizando señal:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.delete('/api/signals/:id', async (req, res) => {
    const { id } = req.params;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const deletedSignal = await Signal.findByIdAndDelete(id);

            if (deletedSignal) {
                res.json({ success: true });
            } else {
                res.status(404).json({ error: 'Señal no encontrada' });
            }
        } else {
            // Usar SQLite fallback
            sqliteDb.run('DELETE FROM signals WHERE id = ?', [id], function(err) {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else {
                    res.json({ success: true });
                }
            });
        }
    } catch (error) {
        console.error('Error eliminando señal:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// ==================== RUTAS DE NOTICIAS ====================

app.get('/api/news', async (req, res) => {
    try {
        if (mongoConnected) {
            // Usar MongoDB
            const news = await News.find({ isActive: true })
                .sort({ createdAt: -1 })
                .limit(20);

            res.json(news);
        } else {
            // Usar SQLite fallback
            sqliteDb.all('SELECT * FROM news ORDER BY created_at DESC LIMIT 20', (err, news) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else {
                    res.json(news);
                }
            });
        }
    } catch (error) {
        console.error('Error obteniendo noticias:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/news', async (req, res) => {
    const { title, content, category, impact, currency, source } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const newNews = new News({
                title,
                content,
                category,
                impact,
                currency: currency ? currency.toUpperCase() : '',
                source
            });

            const savedNews = await newNews.save();
            res.json({ success: true, id: savedNews._id });
        } else {
            // Usar SQLite fallback
            sqliteDb.run('INSERT INTO news (title, content, category, impact, currency, source) VALUES (?, ?, ?, ?, ?, ?)',
                [title, content, category, impact, currency, source],
                function(err) {
                    if (err) {
                        res.status(500).json({ error: 'Error del servidor' });
                    } else {
                        res.json({ success: true, id: this.lastID });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error creando noticia:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// ==================== RUTAS DE ALERTAS ====================

app.get('/api/alerts', async (req, res) => {
    try {
        if (mongoConnected) {
            // Usar MongoDB
            const alerts = await Alert.find({ status: 'active' })
                .populate('user_id', 'name email')
                .sort({ createdAt: -1 })
                .limit(50);

            res.json(alerts);
        } else {
            // Usar SQLite fallback
            sqliteDb.all('SELECT * FROM alerts ORDER BY created_at DESC LIMIT 50', (err, alerts) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else {
                    res.json(alerts);
                }
            });
        }
    } catch (error) {
        console.error('Error obteniendo alertas:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.post('/api/alerts', async (req, res) => {
    const { user_id, type, symbol, message, level } = req.body;

    try {
        if (mongoConnected) {
            // Usar MongoDB
            const newAlert = new Alert({
                user_id,
                type,
                symbol: symbol ? symbol.toUpperCase() : '',
                message,
                level
            });

            const savedAlert = await newAlert.save();
            res.json({ success: true, id: savedAlert._id });
        } else {
            // Usar SQLite fallback
            sqliteDb.run('INSERT INTO alerts (user_id, type, symbol, message, level) VALUES (?, ?, ?, ?, ?)',
                [user_id, type, symbol, message, level],
                function(err) {
                    if (err) {
                        res.status(500).json({ error: 'Error del servidor' });
                    } else {
                        res.json({ success: true, id: this.lastID });
                    }
                }
            );
        }
    } catch (error) {
        console.error('Error creando alerta:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

// Ruta para servir archivos HTML
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'index.html'));
});

// Ruta de estado de la base de datos
app.get('/api/db-status', (req, res) => {
    res.json({
        mongodb: mongoConnected,
        sqlite: sqliteDb !== null,
        activeDB: mongoConnected ? 'MongoDB' : 'SQLite'
    });
});

// ==================== RUTAS AWS ====================

// RUTAS DE MYFXBOOK
app.get('/api/myfxbook/portfolio', async (req, res) => {
    try {
        const result = await myfxbookService.getPortfolioData();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo datos del portfolio' });
    }
});

app.get('/api/myfxbook/trades', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const result = await myfxbookService.getTradeHistory(limit);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo historial de trades' });
    }
});

app.get('/api/myfxbook/stats', async (req, res) => {
    try {
        const result = await myfxbookService.getPerformanceStats();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo estadísticas' });
    }
});

app.get('/api/myfxbook/balance', async (req, res) => {
    try {
        const result = await myfxbookService.getRealTimeBalance();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo balance en tiempo real' });
    }
});

// RUTAS DE NOTICIAS FOREX
app.get('/api/forex/news', async (req, res) => {
    try {
        const result = await forexNewsService.getRealTimeNews();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo noticias' });
    }
});

app.get('/api/forex/calendar', async (req, res) => {
    try {
        const result = await forexNewsService.getEconomicCalendar();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo calendario económico' });
    }
});

app.get('/api/forex/alerts', async (req, res) => {
    try {
        const result = await forexNewsService.getMarketAlerts();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo alertas de mercado' });
    }
});

app.get('/api/forex/sentiment', async (req, res) => {
    try {
        const result = await forexNewsService.getMarketSentiment();
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo sentimiento del mercado' });
    }
});

// RUTAS AWS ALTERNATIVAS (para cuando AWS esté configurado)
app.get('/api/aws/signals', async (req, res) => {
    try {
        const signals = await awsServices.getSignals();
        res.json({ success: true, data: signals });
    } catch (error) {
        res.status(500).json({ error: 'Error obteniendo señales de AWS' });
    }
});

app.post('/api/aws/signals', async (req, res) => {
    try {
        const result = await awsServices.createSignal(req.body);
        res.json(result);
    } catch (error) {
        res.status(500).json({ error: 'Error creando señal en AWS' });
    }
});

// RUTAS ADICIONALES PARA ADMIN PANEL
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.get('/api/users', async (req, res) => {
    try {
        if (mongoConnected) {
            // Usar MongoDB
            const users = await User.find()
                .select('-password') // Excluir password por seguridad
                .sort({ createdAt: -1 });

            res.json(users);
        } else {
            // Usar SQLite fallback
            sqliteDb.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else {
                    res.json(users);
                }
            });
        }
    } catch (error) {
        console.error('Error obteniendo usuarios:', error);
        res.status(500).json({ error: 'Error del servidor' });
    }
});

app.get('/api/aws/users', async (req, res) => {
    try {
        const users = await awsServices.getUsers();
        res.json({ success: true, data: users });
    } catch (error) {
        console.log('AWS no disponible, usando base de datos local');
        // Fallback a base de datos local
        if (mongoConnected) {
            const users = await User.find().select('-password').sort({ createdAt: -1 });
            res.json({ success: true, data: users });
        } else {
            sqliteDb.all('SELECT * FROM users ORDER BY created_at DESC', (err, users) => {
                if (err) {
                    res.status(500).json({ error: 'Error del servidor' });
                } else {
                    res.json({ success: true, data: users });
                }
            });
        }
    }
});

app.get('/api/aws/test', async (req, res) => {
    try {
        // Probar conexión con DynamoDB
        const testResult = await awsServices.testConnection();
        res.json({ success: true, aws: 'connected', result: testResult });
    } catch (error) {
        res.status(500).json({ success: false, aws: 'disconnected', error: error.message });
    }
});

app.post('/api/aws/init', async (req, res) => {
    try {
        console.log('🔄 Inicializando AWS desde admin panel...');
        await createTablesIfNotExist();
        await createS3BucketIfNotExists();
        res.json({ success: true, message: 'AWS inicializado correctamente' });
    } catch (error) {
        console.error('Error inicializando AWS:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Inicializar servicios
async function initializeServices() {
    try {
        console.log('🔄 Inicializando bases de datos...');
        await initializeDatabases();

        console.log('🔄 Inicializando servicios AWS...');
        await createTablesIfNotExist();
        await createS3BucketIfNotExists();

        console.log('🔄 Iniciando monitoreo de noticias...');
        forexNewsService.startNewsMonitoring();

        console.log('✅ Todos los servicios inicializados correctamente');
    } catch (error) {
        console.error('❌ Error inicializando servicios:', error);
    }
}

// Iniciar servidor
app.listen(PORT, async () => {
    console.log(`🚀 Servidor GERMAYORI corriendo en http://localhost:${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}/dashboard.html`);
    console.log(`🔗 MyFXBook Portfolio: https://www.myfxbook.com/portfolio/germayori/11537608`);

    // Inicializar servicios
    await initializeServices();
});

// Manejar cierre graceful
process.on('SIGINT', async () => {
    console.log('🔄 Cerrando servidor GERMAYORI...');

    // Detener monitoreo de noticias
    forexNewsService.stopNewsMonitoring();

    // Cerrar MongoDB
    if (mongoConnected) {
        await closeDB();
    }

    // Cerrar SQLite
    if (sqliteDb) {
        sqliteDb.close((err) => {
            if (err) {
                console.error(err.message);
            } else {
                console.log('✅ Conexión SQLite cerrada');
            }
        });
    }

    console.log('✅ Servidor GERMAYORI cerrado correctamente');
    process.exit(0);
});
