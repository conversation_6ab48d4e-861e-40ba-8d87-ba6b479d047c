// controllers/chatController.js
const { OpenAI } = require('openai');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Nueva versión para dar señales con imagen
exports.sendImageSignal = async (req, res) => {
  const user = req.user; // usuario autenticado

  if (!user || !user.isPaid) {
    return res.status(403).json({ error: 'Acceso restringido a usuarios VIP' });
  }

  try {
    const { message } = req.body;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: 'Imagen requerida' });
    }

    const imageData = fs.readFileSync(path.join(__dirname, '..', 'uploads', file.filename));

    const response = await openai.chat.completions.create({
      model: 'gpt-4-vision-preview',
      messages: [
        {
          role: 'system',
          content: `<PERSON><PERSON>, una inteligencia experta en trading institucional.
Recibirás una imagen de un gráfico, analiza la estructura, liquidez, FVG y BOS.
Devuelve una entrada clara con:
- Tipo de entrada (Compra/Venta)
- Justificación
- Nivel de entrada
- SL y TP sugeridos
No incluyas disclaimers.`
        },
        {
          role: 'user',
          content: [
            { type: 'text', text: message || 'Analiza esta imagen y dame una entrada completa.' },
            {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${imageData.toString('base64')}`,
                detail: 'high'
              }
            }
          ]
        }
      ],
      max_tokens: 1000
    });

    res.json({ signal: response.choices[0].message.content });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Error al analizar la imagen' });
  }
};
