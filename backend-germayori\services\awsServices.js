const { dynamodb, s3, <PERSON><PERSON><PERSON><PERSON>, S3_BUCKET } = require('../config/aws');
const { v4: uuidv4 } = require('uuid');

class AWSServices {
    // ==================== USUARIOS ====================
    async createUser(userData) {
        const user = {
            id: uuidv4(),
            ...userData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };

        const params = {
            TableName: TABLES.USERS,
            Item: user
        };

        try {
            await dynamodb.put(params).promise();
            return { success: true, user };
        } catch (error) {
            console.error('Error creando usuario:', error);
            return { success: false, error: error.message };
        }
    }

    async getUserByEmail(email) {
        const params = {
            TableName: TABLES.USERS,
            IndexName: 'email-index',
            KeyConditionExpression: 'email = :email',
            ExpressionAttributeValues: {
                ':email': email
            }
        };

        try {
            const result = await dynamodb.query(params).promise();
            return result.Items[0] || null;
        } catch (error) {
            console.error('Error obteniendo usuario:', error);
            return null;
        }
    }

    async getUserById(id) {
        const params = {
            TableName: TABLES.USERS,
            Key: { id }
        };

        try {
            const result = await dynamodb.get(params).promise();
            return result.Item || null;
        } catch (error) {
            console.error('Error obteniendo usuario por ID:', error);
            return null;
        }
    }

    async getUsers(limit = 50) {
        const params = {
            TableName: TABLES.USERS,
            Limit: limit
        };

        try {
            const result = await dynamodb.scan(params).promise();
            return result.Items.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error obteniendo usuarios:', error);
            return [];
        }
    }

    // ==================== SEÑALES ====================
    async createSignal(signalData) {
        const signal = {
            id: uuidv4(),
            ...signalData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            status: signalData.status || 'active'
        };

        const params = {
            TableName: TABLES.SIGNALS,
            Item: signal
        };

        try {
            await dynamodb.put(params).promise();
            return { success: true, signal };
        } catch (error) {
            console.error('Error creando señal:', error);
            return { success: false, error: error.message };
        }
    }

    async getSignals(limit = 50) {
        const params = {
            TableName: TABLES.SIGNALS,
            ScanIndexForward: false, // Orden descendente
            Limit: limit
        };

        try {
            const result = await dynamodb.scan(params).promise();
            return result.Items.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error obteniendo señales:', error);
            return [];
        }
    }

    async getSignalsByUser(userId, limit = 20) {
        const params = {
            TableName: TABLES.SIGNALS,
            IndexName: 'user-signals-index',
            KeyConditionExpression: 'user_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false,
            Limit: limit
        };

        try {
            const result = await dynamodb.query(params).promise();
            return result.Items;
        } catch (error) {
            console.error('Error obteniendo señales del usuario:', error);
            return [];
        }
    }

    async updateSignal(id, updateData) {
        const params = {
            TableName: TABLES.SIGNALS,
            Key: { id },
            UpdateExpression: 'SET #status = :status, #pips = :pips, #updated_at = :updated_at',
            ExpressionAttributeNames: {
                '#status': 'status',
                '#pips': 'pips',
                '#updated_at': 'updated_at'
            },
            ExpressionAttributeValues: {
                ':status': updateData.status,
                ':pips': updateData.pips || 0,
                ':updated_at': new Date().toISOString()
            },
            ReturnValues: 'ALL_NEW'
        };

        try {
            const result = await dynamodb.update(params).promise();
            return { success: true, signal: result.Attributes };
        } catch (error) {
            console.error('Error actualizando señal:', error);
            return { success: false, error: error.message };
        }
    }

    async deleteSignal(id) {
        const params = {
            TableName: TABLES.SIGNALS,
            Key: { id }
        };

        try {
            await dynamodb.delete(params).promise();
            return { success: true };
        } catch (error) {
            console.error('Error eliminando señal:', error);
            return { success: false, error: error.message };
        }
    }

    // ==================== NOTICIAS ====================
    async createNews(newsData) {
        const news = {
            id: uuidv4(),
            ...newsData,
            created_at: new Date().toISOString()
        };

        const params = {
            TableName: TABLES.NEWS,
            Item: news
        };

        try {
            await dynamodb.put(params).promise();
            return { success: true, news };
        } catch (error) {
            console.error('Error creando noticia:', error);
            return { success: false, error: error.message };
        }
    }

    async getNews(limit = 20) {
        const params = {
            TableName: TABLES.NEWS,
            Limit: limit
        };

        try {
            const result = await dynamodb.scan(params).promise();
            return result.Items.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error obteniendo noticias:', error);
            return [];
        }
    }

    // ==================== ALERTAS ====================
    async createAlert(alertData) {
        const alert = {
            id: uuidv4(),
            ...alertData,
            created_at: new Date().toISOString(),
            status: alertData.status || 'active'
        };

        const params = {
            TableName: TABLES.ALERTS,
            Item: alert
        };

        try {
            await dynamodb.put(params).promise();
            return { success: true, alert };
        } catch (error) {
            console.error('Error creando alerta:', error);
            return { success: false, error: error.message };
        }
    }

    async getAlerts(limit = 50) {
        const params = {
            TableName: TABLES.ALERTS,
            Limit: limit
        };

        try {
            const result = await dynamodb.scan(params).promise();
            return result.Items.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        } catch (error) {
            console.error('Error obteniendo alertas:', error);
            return [];
        }
    }

    async getAlertsByUser(userId, limit = 20) {
        const params = {
            TableName: TABLES.ALERTS,
            IndexName: 'user-alerts-index',
            KeyConditionExpression: 'user_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            ScanIndexForward: false,
            Limit: limit
        };

        try {
            const result = await dynamodb.query(params).promise();
            return result.Items;
        } catch (error) {
            console.error('Error obteniendo alertas del usuario:', error);
            return [];
        }
    }

    // ==================== S3 STORAGE ====================
    async uploadFile(file, folder = 'general') {
        const key = `${folder}/${uuidv4()}-${file.originalname}`;

        const params = {
            Bucket: S3_BUCKET,
            Key: key,
            Body: file.buffer,
            ContentType: file.mimetype,
            ACL: 'public-read'
        };

        try {
            const result = await s3.upload(params).promise();
            return { success: true, url: result.Location, key: result.Key };
        } catch (error) {
            console.error('Error subiendo archivo:', error);
            return { success: false, error: error.message };
        }
    }

    async deleteFile(key) {
        const params = {
            Bucket: S3_BUCKET,
            Key: key
        };

        try {
            await s3.deleteObject(params).promise();
            return { success: true };
        } catch (error) {
            console.error('Error eliminando archivo:', error);
            return { success: false, error: error.message };
        }
    }

    // ==================== TESTING Y UTILIDADES ====================
    async testConnection() {
        try {
            // Probar conexión con DynamoDB listando tablas
            const params = {
                TableName: TABLES.USERS,
                Limit: 1
            };

            await dynamodb.scan(params).promise();
            return {
                dynamodb: 'connected',
                timestamp: new Date().toISOString(),
                tables: Object.values(TABLES)
            };
        } catch (error) {
            console.error('Error probando conexión AWS:', error);
            throw new Error(`AWS Connection failed: ${error.message}`);
        }
    }
}

module.exports = new AWSServices();
